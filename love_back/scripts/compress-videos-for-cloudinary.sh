#!/bin/bash

# Cloudinary视频压缩脚本
# 将大视频文件压缩到100MB以下，适配免费计划

echo "🎬 开始压缩视频文件以适配Cloudinary免费计划..."

# 创建输出目录
mkdir -p background/cloudinary-ready

# 压缩anniversary.mp4 (570MB -> <100MB) - 保持2K画质
echo "📹 压缩anniversary.mp4 (保持2K画质)..."
ffmpeg -i background/anniversary/anniversary.mp4 \
    -c:v libx264 \
    -crf 23 \
    -preset slow \
    -c:a aac \
    -b:a 192k \
    -vf "scale=2560:1440:flags=lanczos" \
    -movflags +faststart \
    -profile:v high \
    -level 4.1 \
    background/cloudinary-ready/anniversary.mp4

# 压缩together-days.mp4 (146MB -> <100MB) - 保持高画质
echo "📹 压缩together-days.mp4 (保持高画质)..."
ffmpeg -i background/together-days/together-days.mp4 \
    -c:v libx264 \
    -crf 20 \
    -preset slow \
    -c:a aac \
    -b:a 192k \
    -vf "scale=2560:1440:flags=lanczos" \
    -movflags +faststart \
    -profile:v high \
    -level 4.1 \
    background/cloudinary-ready/together-days.mp4

# 复制已符合大小要求的文件
echo "📁 复制符合要求的视频文件..."
cp background/home/<USER>/cloudinary-ready/
cp background/meetings/meetings.mp4 background/cloudinary-ready/
cp background/memorial/memorial.mp4 background/cloudinary-ready/

# 检查文件大小
echo "📊 压缩后文件大小："
ls -lh background/cloudinary-ready/*.mp4

echo "✅ 视频压缩完成！所有文件已准备好上传到Cloudinary"
