/**
 * Global Video Manager - 全局视频管理器
 * 解决页面切换时视频资源重复清理导致的加载缓慢问题
 * 实现跨页面视频缓存和智能预加载机制
 */

(function() {
    'use strict';

    // 页面主题映射 - 基于现有的video-background-handler.js
    const PAGE_THEMES = {
        'index.html': 'flower',
        'anniversary.html': 'green', 
        'meetings.html': 'stars',
        'memorial.html': 'sea',
        'together-days.html': 'lake'
    };

    // 网络类型枚举
    const NETWORK_TYPES = {
        FAST: 'fast',      // WiFi, 4G
        SLOW: 'slow',      // 3G, 2G
        UNKNOWN: 'unknown'
    };

    // 视频状态枚举
    const VIDEO_STATES = {
        IDLE: 'idle',
        LOADING: 'loading',
        LOADED: 'loaded',
        ERROR: 'error',
        CACHED: 'cached'
    };

    class VideoManager {
        constructor() {
            if (VideoManager.instance) {
                return VideoManager.instance;
            }

            // 单例模式
            VideoManager.instance = this;

            // 初始化属性
            this.videoCache = new Map();           // 视频缓存
            this.loadingQueue = new Map();         // 加载队列
            this.preloadQueue = [];                // 预加载队列
            this.maxCacheSize = 4;                 // 最大缓存数量
            this.networkType = this.detectNetworkType();
            this.eventListeners = new Map();      // 事件监听器
            this.hasPreloadedOnce = false;        // 防止重复预加载

            // 初始化
            this.init();
        }

        /**
         * 初始化视频管理器
         */
        init() {
            console.log('🎬 VideoManager 初始化');
            
            // 监听网络状态变化
            this.setupNetworkMonitoring();
            
            // 监听页面可见性变化
            this.setupVisibilityMonitoring();
            
            // 设置内存清理
            this.setupMemoryManagement();
        }

        /**
         * 检测网络类型
         */
        detectNetworkType() {
            const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;
            
            if (!connection) {
                return NETWORK_TYPES.UNKNOWN;
            }

            const effectiveType = connection.effectiveType;
            if (effectiveType === '4g' || effectiveType === 'wifi') {
                return NETWORK_TYPES.FAST;
            } else if (effectiveType === '3g' || effectiveType === '2g' || effectiveType === 'slow-2g') {
                return NETWORK_TYPES.SLOW;
            }

            return NETWORK_TYPES.UNKNOWN;
        }

        /**
         * 设置网络监控
         */
        setupNetworkMonitoring() {
            const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;
            
            if (connection) {
                connection.addEventListener('change', () => {
                    const oldType = this.networkType;
                    this.networkType = this.detectNetworkType();
                    
                    if (oldType !== this.networkType) {
                        console.log(`📶 网络类型变化: ${oldType} -> ${this.networkType}`);
                        this.adjustPreloadStrategy();
                    }
                });
            }
        }

        /**
         * 设置页面可见性监控
         */
        setupVisibilityMonitoring() {
            document.addEventListener('visibilitychange', () => {
                if (document.hidden) {
                    this.pauseAllVideos();
                } else {
                    this.resumeCurrentVideo();
                }
            });
        }

        /**
         * 设置内存管理
         */
        setupMemoryManagement() {
            // 定期清理未使用的视频缓存
            setInterval(() => {
                this.cleanupUnusedCache();
            }, 60000); // 每分钟检查一次
        }

        /**
         * 获取当前页面主题
         */
        getCurrentTheme() {
            const path = window.location.pathname;
            const filename = path.split('/').pop() || 'index.html';
            return PAGE_THEMES[filename] || 'flower';
        }

        /**
         * 获取当前页面的配置key
         */
        getCurrentPageKey() {
            const path = window.location.pathname;
            const filename = path.split('/').pop() || 'index.html';

            // 页面文件名到配置key的映射
            const pageKeyMap = {
                'index.html': 'INDEX',
                'meetings.html': 'MEETINGS',
                'anniversary.html': 'ANNIVERSARY',
                'memorial.html': 'MEMORIAL',
                'together-days.html': 'TOGETHER_DAYS'
            };

            return pageKeyMap[filename] || 'INDEX';
        }

        /**
         * 创建视频元素（优化版本）
         */
        createVideoElement(videoConfig) {
            const video = document.createElement('video');
            video.autoplay = false; // 改为手动控制播放
            video.muted = true;
            video.loop = true;
            video.playsInline = true;
            video.preload = this.getPreloadStrategy();
            video.className = 'video-background';

            // 设置视频样式（添加性能优化）
            video.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                object-fit: cover;
                z-index: -1;
                opacity: 0;
                transition: opacity 0.8s ease-in-out;
                transform: translateZ(0);
                backface-visibility: hidden;
                will-change: opacity;
            `;

            // 获取最佳视频URL（支持自适应质量）
            const optimalUrl = this.getOptimalVideoUrl(videoConfig);
            video.src = optimalUrl;

            // 添加加载进度监听
            this.addProgressTracking(video, videoConfig);

            return video;
        }

        /**
         * 获取最佳视频URL（自适应质量）
         */
        getOptimalVideoUrl(videoConfig) {
            // 如果配置支持多分辨率
            if (videoConfig.files && typeof videoConfig.files === 'object') {
                const quality = this.determineOptimalQuality();
                const selectedFile = videoConfig.files[quality] || videoConfig.files.medium || videoConfig.files.high;
                return `${window.CONFIG.PATHS.BACKGROUND}/${selectedFile}`;
            }

            // 兼容旧配置格式
            return videoConfig.url || `${window.CONFIG.PATHS.BACKGROUND}/${videoConfig.file}`;
        }

        /**
         * 确定最佳视频质量
         */
        determineOptimalQuality() {
            // 检查设备性能
            const deviceInfo = this.getDeviceInfo();

            // 检查网络状况
            const networkInfo = this.getNetworkInfo();

            // 检查屏幕分辨率
            const screenInfo = this.getScreenInfo();

            console.log('📊 设备信息:', { deviceInfo, networkInfo, screenInfo });

            // 综合判断最佳质量
            if (deviceInfo.isLowEnd || networkInfo.isSlow) {
                return 'low';
            } else if (deviceInfo.isHighEnd && networkInfo.isFast && screenInfo.isHighRes) {
                return 'high';
            } else {
                return 'medium';
            }
        }

        /**
         * 获取设备信息
         */
        getDeviceInfo() {
            return {
                cores: navigator.hardwareConcurrency || 2,
                memory: navigator.deviceMemory || 4,
                isLowEnd: (navigator.hardwareConcurrency || 2) <= 2 || (navigator.deviceMemory || 4) <= 2,
                isHighEnd: (navigator.hardwareConcurrency || 2) >= 8 && (navigator.deviceMemory || 4) >= 8,
                isMobile: /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
            };
        }

        /**
         * 获取网络信息
         */
        getNetworkInfo() {
            const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;

            if (connection) {
                return {
                    effectiveType: connection.effectiveType,
                    downlink: connection.downlink,
                    rtt: connection.rtt,
                    isSlow: connection.effectiveType === '2g' || connection.downlink < 1.5,
                    isFast: connection.effectiveType === '4g' && connection.downlink > 10
                };
            }

            // 默认假设中等网络
            return {
                effectiveType: '3g',
                downlink: 5,
                rtt: 100,
                isSlow: false,
                isFast: false
            };
        }

        /**
         * 获取屏幕信息
         */
        getScreenInfo() {
            return {
                width: screen.width,
                height: screen.height,
                pixelRatio: window.devicePixelRatio || 1,
                isHighRes: (screen.width * (window.devicePixelRatio || 1)) >= 1920
            };
        }

        /**
         * 添加进度跟踪
         */
        addProgressTracking(video, videoConfig) {
            let lastProgress = 0;

            video.addEventListener('progress', () => {
                if (video.buffered.length > 0) {
                    const progress = Math.round((video.buffered.end(0) / video.duration) * 100);
                    if (progress > lastProgress) {
                        lastProgress = progress;

                        // 通知性能优化器
                        if (window.VideoPerformanceOptimizer) {
                            window.VideoPerformanceOptimizer.showLoadingProgress(videoConfig.name || 'Video', progress);
                        }

                        console.log(`📊 ${videoConfig.name || 'Video'} 加载进度: ${progress}%`);
                    }
                }
            });
        }

        /**
         * 获取预加载策略
         */
        getPreloadStrategy() {
            switch (this.networkType) {
                case NETWORK_TYPES.FAST:
                    return 'auto';
                case NETWORK_TYPES.SLOW:
                    return 'metadata';
                default:
                    return 'metadata';
            }
        }

        /**
         * 加载视频（优化缓存复用）
         */
        async loadVideo(pageKey, videoConfig) {
            console.log(`🎬 开始加载视频: ${pageKey}`);

            // 检查缓存
            if (this.videoCache.has(pageKey)) {
                const cachedVideo = this.videoCache.get(pageKey);
                console.log(`🔍 检查缓存视频: ${pageKey}`, {
                    readyState: cachedVideo.readyState,
                    networkState: cachedVideo.networkState,
                    hasError: !!cachedVideo.error
                });

                // 如果视频已经可以播放，直接返回
                if (cachedVideo.readyState >= 3 && !cachedVideo.error) {
                    console.log(`✅ 使用缓存视频: ${pageKey}`);
                    // 确保视频在DOM中
                    if (!cachedVideo.parentNode) {
                        document.body.appendChild(cachedVideo);
                    }
                    return cachedVideo;
                }

                // 如果缓存的视频有问题，清除它
                if (cachedVideo.error) {
                    console.log(`🗑️ 清除有问题的缓存视频: ${pageKey}`);
                    this.videoCache.delete(pageKey);
                    if (cachedVideo.parentNode) {
                        cachedVideo.parentNode.removeChild(cachedVideo);
                    }
                }
            }

            // 检查是否正在加载
            if (this.loadingQueue.has(pageKey)) {
                console.log(`⏳ 视频正在加载中: ${pageKey}`);
                return this.loadingQueue.get(pageKey);
            }

            // 创建加载Promise
            const loadPromise = this.createLoadPromise(pageKey, videoConfig);
            this.loadingQueue.set(pageKey, loadPromise);

            try {
                const video = await loadPromise;
                this.loadingQueue.delete(pageKey);
                return video;
            } catch (error) {
                this.loadingQueue.delete(pageKey);
                throw error;
            }
        }

        /**
         * 创建加载Promise（带重试机制）
         */
        createLoadPromise(pageKey, videoConfig, retryCount = 0) {
            const maxRetries = 2;

            return new Promise(async (resolve, reject) => {
                const video = this.createVideoElement(videoConfig);

                // 设置加载成功回调
                const onLoadSuccess = () => {
                    video.classList.add('loaded');
                    this.addToCache(pageKey, video);
                    console.log(`✅ 视频加载成功: ${pageKey}${retryCount > 0 ? ` (重试${retryCount}次后成功)` : ''}`);
                    resolve(video);
                };

                // 设置加载失败回调
                const onLoadError = async (error) => {
                    console.log(`❌ 视频加载失败: ${pageKey}`, error);

                    // 如果还有重试次数，则重试
                    if (retryCount < maxRetries) {
                        console.log(`🔄 重试加载视频: ${pageKey} (${retryCount + 1}/${maxRetries})`);
                        try {
                            // 等待一段时间后重试
                            await new Promise(resolve => setTimeout(resolve, 1000 * (retryCount + 1)));
                            const retryVideo = await this.createLoadPromise(pageKey, videoConfig, retryCount + 1);
                            resolve(retryVideo);
                            return;
                        } catch (retryError) {
                            // 重试失败，继续执行下面的降级逻辑
                        }
                    }

                    // 所有重试都失败，应用备用背景
                    this.applyFallbackBackground(this.getCurrentTheme());
                    reject(error);
                };

                // 监听加载事件
                video.addEventListener('canplaythrough', onLoadSuccess, { once: true });
                video.addEventListener('error', onLoadError, { once: true });

                // 设置超时（根据网络类型、重试次数和页面优先级调整）
                let baseTimeout;
                if (pageKey === 'INDEX') {
                    // 首页视频给更长的超时时间
                    baseTimeout = this.networkType === NETWORK_TYPES.SLOW ? 60000 : 45000;
                } else {
                    baseTimeout = this.networkType === NETWORK_TYPES.SLOW ? 30000 : 20000;
                }
                const timeout = baseTimeout + (retryCount * 10000); // 重试时大幅增加超时时间

                setTimeout(() => {
                    if (!video.classList.contains('loaded')) {
                        onLoadError(new Error('视频加载超时'));
                    }
                }, timeout);

                // 开始加载
                video.load();
            });
        }

        /**
         * 添加到缓存
         */
        addToCache(pageKey, video) {
            // 检查缓存大小，实现LRU淘汰
            if (this.videoCache.size >= this.maxCacheSize) {
                const firstKey = this.videoCache.keys().next().value;
                const oldVideo = this.videoCache.get(firstKey);
                
                // 清理旧视频
                if (oldVideo && oldVideo.parentNode) {
                    oldVideo.parentNode.removeChild(oldVideo);
                }
                
                this.videoCache.delete(firstKey);
                console.log(`🗑️ 清理旧视频缓存: ${firstKey}`);
            }

            this.videoCache.set(pageKey, video);
            console.log(`💾 视频已缓存: ${pageKey}`);
        }

        /**
         * 应用备用背景 - 基于现有逻辑
         */
        applyFallbackBackground(theme) {
            const videoContainer = document.querySelector('.video-background');
            if (videoContainer) {
                videoContainer.classList.add(`fallback-${theme}`);
                console.log(`🎨 应用${theme}主题备用背景`);
                
                // 隐藏加载遮罩
                const loadingOverlay = document.getElementById('loadingOverlay');
                if (loadingOverlay) {
                    loadingOverlay.classList.add('hidden');
                }
            }
        }

        /**
         * 预加载视频
         */
        async preloadVideo(pageKey, videoConfig, priority = 1) {
            // 检查是否已缓存
            if (this.videoCache.has(pageKey)) {
                return;
            }

            // 检查网络状况
            if (this.networkType === NETWORK_TYPES.SLOW) {
                console.log(`📶 慢网络，跳过预加载: ${pageKey}`);
                return;
            }

            console.log(`🔄 预加载视频: ${pageKey}`);
            
            try {
                await this.loadVideo(pageKey, videoConfig);
            } catch (error) {
                console.log(`⚠️ 预加载失败: ${pageKey}`, error);
            }
        }

        /**
         * 调整预加载策略
         */
        adjustPreloadStrategy() {
            if (this.networkType === NETWORK_TYPES.SLOW) {
                // 慢网络下清空预加载队列
                this.preloadQueue = [];
                console.log('📶 慢网络，清空预加载队列');
            }
        }

        /**
         * 开始预加载其他页面视频（优化版本）
         */
        startPreloading() {
            // 如果是慢网络，跳过预加载
            if (this.networkType === NETWORK_TYPES.SLOW) {
                console.log('📶 慢网络，跳过预加载');
                return;
            }

            const currentPageKey = this.getCurrentPageKey();
            const allPages = Object.keys(window.CONFIG?.VIDEOS?.PAGES || {});

            // 按优先级排序，排除当前页面
            const preloadPages = allPages
                .filter(pageKey => pageKey !== currentPageKey)
                .map(pageKey => ({
                    key: pageKey,
                    config: window.CONFIG.VIDEOS.PAGES[pageKey],
                    priority: window.CONFIG.VIDEOS.PAGES[pageKey].priority || 999
                }))
                .sort((a, b) => a.priority - b.priority);

            console.log(`🚀 开始预加载 ${preloadPages.length} 个页面视频`);

            // 延迟预加载，避免与首页视频竞争带宽
            setTimeout(() => {
                this.preloadWithThrottling(preloadPages);
            }, 2000); // 延迟2秒开始预加载
        }

        /**
         * 节流预加载 - 避免同时加载太多视频
         */
        async preloadWithThrottling(preloadPages) {
            const maxConcurrent = this.networkType === NETWORK_TYPES.FAST ? 2 : 1;
            let currentIndex = 0;
            let activeLoads = 0;

            const loadNext = async () => {
                if (currentIndex >= preloadPages.length || activeLoads >= maxConcurrent) {
                    return;
                }

                const page = preloadPages[currentIndex++];
                activeLoads++;

                try {
                    console.log(`📥 预加载视频: ${page.key} (优先级: ${page.priority})`);
                    await this.preloadVideo(page.key, page.config, page.priority);
                    console.log(`✅ 预加载完成: ${page.key}`);
                } catch (error) {
                    console.log(`❌ 预加载失败: ${page.key}`, error);
                } finally {
                    activeLoads--;
                    // 继续加载下一个
                    setTimeout(loadNext, 500); // 间隔500ms避免过于频繁
                }
            };

            // 启动初始加载
            for (let i = 0; i < maxConcurrent && i < preloadPages.length; i++) {
                loadNext();
            }
        }

        /**
         * 暂停所有视频
         */
        pauseAllVideos() {
            this.videoCache.forEach((video, key) => {
                if (video && !video.paused) {
                    video.pause();
                    console.log(`⏸️ 暂停视频: ${key}`);
                }
            });
        }

        /**
         * 为页面加载视频（优化版本）
         */
        async loadVideoForPage(pageKey) {
            const config = window.CONFIG?.VIDEOS?.PAGES?.[pageKey];
            if (!config) {
                console.log(`❌ 页面配置不存在: ${pageKey}`);
                return;
            }

            try {
                const video = await this.loadVideo(pageKey, config);

                // 隐藏其他视频
                this.hideOtherVideos(pageKey);

                // 确保视频在DOM中
                if (!video.parentNode) {
                    document.body.appendChild(video);
                }

                // 设置视频为当前显示的视频
                video.style.zIndex = '-1';
                video.style.opacity = '0';

                // 淡入显示
                setTimeout(() => {
                    video.style.opacity = '1';
                    video.play().catch(error => {
                        console.log('自动播放失败:', error);
                    });
                }, 100);

                // 延迟开始预加载其他页面视频，确保首页视频优先
                if (!this.hasPreloadedOnce) {
                    // 延迟预加载，让首页视频完全加载完成后再开始
                    setTimeout(() => {
                        this.startPreloading();
                        this.hasPreloadedOnce = true;
                    }, 3000); // 延迟3秒，确保首页视频加载完成
                }

            } catch (error) {
                console.log(`视频加载失败: ${pageKey}`, error);
                this.applyFallbackBackground(this.getCurrentTheme());
            }
        }

        /**
         * 隐藏其他视频
         */
        hideOtherVideos(currentPageKey) {
            this.videoCache.forEach((video, key) => {
                if (key !== currentPageKey && video.parentNode) {
                    video.style.opacity = '0';
                    video.pause();
                }
            });
        }

        /**
         * 恢复当前视频
         */
        resumeCurrentVideo() {
            const currentPageKey = this.getCurrentPageKey();
            const currentVideo = this.videoCache.get(currentPageKey);

            console.log(`🔍 尝试恢复视频: ${currentPageKey}`, {
                hasVideo: !!currentVideo,
                isPaused: currentVideo ? currentVideo.paused : 'N/A',
                cacheKeys: Array.from(this.videoCache.keys())
            });

            if (currentVideo) {
                if (currentVideo.paused) {
                    currentVideo.play().catch(error => {
                        console.log('▶️ 恢复视频播放失败:', error);
                    });
                    console.log(`▶️ 页面显示，恢复当前视频: ${currentPageKey}`);
                } else {
                    console.log(`▶️ 视频已在播放: ${currentPageKey}`);
                }
            } else {
                console.log(`❌ 未找到缓存视频: ${currentPageKey}，尝试重新加载`);
                // 如果缓存中没有视频，尝试重新加载
                this.loadVideoForPage(currentPageKey);
            }
        }

        /**
         * 清理未使用的缓存
         */
        cleanupUnusedCache() {
            const currentPageKey = this.getCurrentPageKey();
            const cacheKeys = Array.from(this.videoCache.keys());

            // 保留当前页面和最近使用的视频
            const keepKeys = [currentPageKey];
            const toRemove = cacheKeys.filter(key => !keepKeys.includes(key));

            if (toRemove.length > 2) { // 只保留最近的2个非当前页面视频
                const removeKeys = toRemove.slice(0, -2);
                removeKeys.forEach(key => {
                    const video = this.videoCache.get(key);
                    if (video && video.parentNode) {
                        video.parentNode.removeChild(video);
                    }
                    this.videoCache.delete(key);
                    console.log(`🧹 清理未使用缓存: ${key}`);
                });
            }
        }

        /**
         * 获取缓存状态
         */
        getCacheStatus() {
            return {
                cacheSize: this.videoCache.size,
                maxCacheSize: this.maxCacheSize,
                networkType: this.networkType,
                cachedVideos: Array.from(this.videoCache.keys())
            };
        }

        /**
         * 清理所有缓存
         */
        clearAllCache() {
            this.videoCache.forEach((video, key) => {
                if (video && video.parentNode) {
                    video.parentNode.removeChild(video);
                }
            });
            
            this.videoCache.clear();
            this.loadingQueue.clear();
            console.log('🧹 已清理所有视频缓存');
        }
    }

    // 防止重复初始化
    if (!window.VideoManager) {
        // 创建全局实例
        window.VideoManager = new VideoManager();
        console.log('🎬 VideoManager 已初始化完成');
    } else {
        console.log('🎬 VideoManager 已存在，跳过重复初始化');
    }

    // 导出到全局
    window.videoManager = window.VideoManager;

})();
