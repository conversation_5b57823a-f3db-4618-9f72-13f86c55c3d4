#!/usr/bin/env node

/**
 * 强制2K视频修复脚本
 * 确保网站使用正确的2K分辨率视频
 * 
 * 问题分析：
 * 1. Cloudinary上的视频确实是2K (2560x1440)
 * 2. 但可能因为q_auto参数导致过度压缩
 * 3. 需要强制使用q_100确保最高质量
 */

// 2K视频URL生成器
function generate2KVideoUrl(cloudName, publicId, forceHighQuality = true) {
    const baseUrl = `https://res.cloudinary.com/${cloudName}/video/upload`;
    
    if (forceHighQuality) {
        // 强制最高质量，2K分辨率
        const transforms = 'q_100,f_auto,w_2560,h_1440,c_limit';
        return `${baseUrl}/${transforms}/${publicId}.mp4`;
    } else {
        // 原始文件，无变换
        return `${baseUrl}/${publicId}.mp4`;
    }
}

// 测试URL可访问性
async function testVideoUrl(url) {
    try {
        const response = await fetch(url, { method: 'HEAD' });
        const contentLength = response.headers.get('content-length');
        const contentType = response.headers.get('content-type');
        
        return {
            url,
            status: response.status,
            success: response.ok,
            size: contentLength ? `${(parseInt(contentLength) / 1024 / 1024).toFixed(2)}MB` : 'Unknown',
            type: contentType
        };
    } catch (error) {
        return {
            url,
            status: 'ERROR',
            success: false,
            error: error.message
        };
    }
}

// 主修复函数
async function fix2KVideo() {
    console.log('🔧 开始2K视频修复...\n');
    
    const videoConfigs = [
        {
            name: '首页视频',
            cloudName: 'dcglebc2w',
            publicId: 'love-website/home',
            page: 'index.html'
        },
        {
            name: '首页视频(备用)',
            cloudName: 'dcglebc2w', 
            publicId: 'love-website/index',
            page: 'index.html'
        }
    ];
    
    console.log('=== 第一步：测试当前URL ===');
    
    for (const config of videoConfigs) {
        console.log(`\n📹 测试 ${config.name}:`);
        
        // 测试高质量2K URL
        const highQualityUrl = generate2KVideoUrl(config.cloudName, config.publicId, true);
        console.log(`🔗 2K高质量URL: ${highQualityUrl}`);
        
        const highQualityResult = await testVideoUrl(highQualityUrl);
        if (highQualityResult.success) {
            console.log(`✅ 可访问 - 大小: ${highQualityResult.size}`);
        } else {
            console.log(`❌ 不可访问 - 状态: ${highQualityResult.status}`);
        }
        
        // 测试原始URL
        const originalUrl = generate2KVideoUrl(config.cloudName, config.publicId, false);
        console.log(`🔗 原始URL: ${originalUrl}`);
        
        const originalResult = await testVideoUrl(originalUrl);
        if (originalResult.success) {
            console.log(`✅ 可访问 - 大小: ${originalResult.size}`);
        } else {
            console.log(`❌ 不可访问 - 状态: ${originalResult.status}`);
        }
    }
    
    console.log('\n=== 第二步：生成修复代码 ===');
    
    // 生成前端修复代码
    const fixCode = `
// 2K视频强制修复代码 - 复制到浏览器控制台执行
function force2KVideoFix() {
    console.log('🔧 强制应用2K视频修复...');
    
    const videos = document.querySelectorAll('video');
    videos.forEach((video, index) => {
        // 使用最高质量2K URL
        const newUrl = '${generate2KVideoUrl('dcglebc2w', 'love-website/home', true)}';
        
        console.log(\`🎬 修复视频\${index + 1}: \${newUrl}\`);
        
        // 添加加载事件监听
        video.addEventListener('loadedmetadata', function() {
            console.log(\`📊 视频\${index + 1}分辨率: \${this.videoWidth}x\${this.videoHeight}\`);
            if (this.videoWidth >= 2560) {
                console.log('✅ 2K分辨率确认！');
            } else {
                console.warn('⚠️ 分辨率低于2K，可能需要清除缓存');
            }
        });
        
        video.src = newUrl;
        video.load();
    });
}

// 立即执行修复
force2KVideoFix();

// 清除浏览器缓存的函数
function clearVideoCache() {
    console.log('🗑️ 清除视频缓存...');
    if ('caches' in window) {
        caches.keys().then(names => {
            names.forEach(name => {
                caches.delete(name);
            });
            console.log('✅ 缓存已清除，请刷新页面');
        });
    }
    
    // 强制刷新页面
    setTimeout(() => {
        window.location.reload(true);
    }, 1000);
}

console.log('💡 如果视频仍不是2K，请执行: clearVideoCache()');
`;
    
    console.log('📋 复制以下代码到浏览器控制台执行：');
    console.log('=' .repeat(60));
    console.log(fixCode);
    console.log('=' .repeat(60));
    
    console.log('\n=== 第三步：推荐的解决方案 ===');
    console.log('1. 🔄 清除浏览器缓存和Cookie');
    console.log('2. 🔧 在浏览器控制台执行上述修复代码');
    console.log('3. 📱 检查设备检测逻辑是否正确');
    console.log('4. 🌐 使用无痕模式测试');
    
    console.log('\n✅ 修复脚本执行完成！');
}

// 执行修复
if (require.main === module) {
    fix2KVideo().catch(console.error);
}

module.exports = { generate2KVideoUrl, testVideoUrl, fix2KVideo };
