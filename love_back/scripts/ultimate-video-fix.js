/**
 * 终极视频修复脚本 - 彻底解决400错误和分辨率问题
 * 包含视频尺寸实时显示功能
 */

// 全局配置
window.UltimateVideoConfig = {
    // 页面配置 - 使用最简化的URL格式
    pages: {
        'INDEX': { cloudName: 'dcglebc2w', publicId: 'love-website/home' },
        'MEETINGS': { cloudName: 'dkqnm9nwr', publicId: 'love-website/meetings' },
        'ANNIVERSARY': { cloudName: 'drhqbbqxz', publicId: 'love-website/anniversary' },
        'MEMORIAL': { cloudName: 'ds14sv2gh', publicId: 'love-website/memorial' },
        'TOGETHER_DAYS': { cloudName: 'dpq95x5nf', publicId: 'love-website/together-days' }
    },
    
    // URL格式优先级（从最简单到最复杂）
    urlFormats: [
        // 格式1：完全无参数（最兼容）
        (cloudName, publicId) => `https://res.cloudinary.com/${cloudName}/video/upload/${publicId}.mp4`,
        
        // 格式2：只有自动质量
        (cloudName, publicId) => `https://res.cloudinary.com/${cloudName}/video/upload/q_auto/${publicId}.mp4`,
        
        // 格式3：质量+格式自动
        (cloudName, publicId) => `https://res.cloudinary.com/${cloudName}/video/upload/q_auto,f_auto/${publicId}.mp4`,
        
        // 格式4：添加缩放（桌面）
        (cloudName, publicId) => `https://res.cloudinary.com/${cloudName}/video/upload/c_scale,w_2560/${publicId}.mp4`,
        
        // 格式5：添加缩放（移动）
        (cloudName, publicId) => `https://res.cloudinary.com/${cloudName}/video/upload/c_scale,w_1920/${publicId}.mp4`
    ],
    
    // 当前使用的格式索引
    currentFormatIndex: 0,
    
    // 视频尺寸显示配置
    showVideoInfo: true,
    videoInfoPosition: 'top-right'
};

// 增强的设备检测
function detectDeviceType() {
    const width = window.innerWidth;
    const height = window.innerHeight;
    const userAgent = navigator.userAgent.toLowerCase();
    
    // 详细的浏览器检测
    const browserInfo = {
        isEdge: userAgent.includes('edge') || userAgent.includes('edg/'),
        isChrome: userAgent.includes('chrome') && !userAgent.includes('edge'),
        isFirefox: userAgent.includes('firefox'),
        isSafari: userAgent.includes('safari') && !userAgent.includes('chrome'),
        isIE: userAgent.includes('trident') || userAgent.includes('msie')
    };
    
    // 设备类型检测
    const isMobileUA = /mobile|android|iphone|ipod|blackberry|iemobile|opera mini/i.test(userAgent);
    const isTabletUA = /ipad|android.*tablet|windows.*touch/i.test(userAgent) && !/mobile/i.test(userAgent);
    
    let deviceType;
    if (width <= 480 || (width <= 768 && isMobileUA && !isTabletUA)) {
        deviceType = 'mobile';
    } else if ((width > 480 && width <= 1024 && isTabletUA) || (width > 768 && width <= 1024 && !isMobileUA)) {
        deviceType = 'tablet';
    } else {
        deviceType = 'desktop';
    }
    
    const deviceInfo = {
        type: deviceType,
        width: width,
        height: height,
        userAgent: userAgent,
        browser: browserInfo,
        isMobile: deviceType === 'mobile',
        isTablet: deviceType === 'tablet',
        isDesktop: deviceType === 'desktop'
    };
    
    console.log('🔍 设备检测结果:', deviceInfo);
    return deviceInfo;
}

// 生成视频URL（使用格式优先级）
function generateVideoUrl(pageKey, formatIndex = null) {
    const config = window.UltimateVideoConfig.pages[pageKey];
    if (!config) {
        console.error('❌ 未找到页面配置:', pageKey);
        return null;
    }
    
    const device = detectDeviceType();
    const useFormatIndex = formatIndex !== null ? formatIndex : window.UltimateVideoConfig.currentFormatIndex;
    const urlFormat = window.UltimateVideoConfig.urlFormats[useFormatIndex];
    
    if (!urlFormat) {
        console.error('❌ URL格式索引超出范围:', useFormatIndex);
        return null;
    }
    
    // 根据设备类型选择合适的格式
    let url;
    if (device.isMobile && useFormatIndex === 4) {
        // 移动设备使用格式5（1920宽度）
        url = window.UltimateVideoConfig.urlFormats[4](config.cloudName, config.publicId);
    } else if (device.isDesktop && useFormatIndex === 4) {
        // 桌面设备使用格式4（2560宽度）
        url = urlFormat(config.cloudName, config.publicId);
    } else {
        url = urlFormat(config.cloudName, config.publicId);
    }
    
    console.log(`🔗 生成URL [格式${useFormatIndex}]:`, url);
    return url;
}

// 测试URL是否可用
async function testVideoUrl(url) {
    try {
        console.log('🧪 测试URL:', url);
        const response = await fetch(url, { method: 'HEAD' });
        const success = response.ok;
        console.log(`${success ? '✅' : '❌'} URL测试结果:`, response.status, url);
        return success;
    } catch (error) {
        console.error('❌ URL测试失败:', error.message, url);
        return false;
    }
}

// 查找可用的URL格式
async function findWorkingUrl(pageKey) {
    console.log('🔍 查找可用URL格式...');
    
    for (let i = 0; i < window.UltimateVideoConfig.urlFormats.length; i++) {
        const url = generateVideoUrl(pageKey, i);
        if (url && await testVideoUrl(url)) {
            window.UltimateVideoConfig.currentFormatIndex = i;
            console.log(`✅ 找到可用格式 [${i}]:`, url);
            return url;
        }
    }
    
    console.error('❌ 所有URL格式都不可用');
    return null;
}

// 创建视频信息显示
function createVideoInfoDisplay() {
    if (!window.UltimateVideoConfig.showVideoInfo) return;
    
    // 移除现有的信息显示
    const existing = document.getElementById('video-info-display');
    if (existing) existing.remove();
    
    const infoDiv = document.createElement('div');
    infoDiv.id = 'video-info-display';
    infoDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 15px;
        border-radius: 8px;
        font-family: 'Courier New', monospace;
        font-size: 12px;
        z-index: 10000;
        max-width: 300px;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
    `;
    
    document.body.appendChild(infoDiv);
    return infoDiv;
}

// 更新视频信息显示
function updateVideoInfoDisplay() {
    const infoDiv = document.getElementById('video-info-display');
    if (!infoDiv) return;
    
    const videos = document.querySelectorAll('video');
    const device = detectDeviceType();
    
    let info = `📱 设备信息:\n`;
    info += `类型: ${device.type} (${device.width}x${device.height})\n`;
    info += `浏览器: ${Object.keys(device.browser).find(key => device.browser[key]) || 'Unknown'}\n\n`;
    
    info += `🎥 视频信息 (${videos.length}个):\n`;
    
    videos.forEach((video, index) => {
        const resolution = video.videoWidth && video.videoHeight ? 
            `${video.videoWidth}x${video.videoHeight}` : '未加载';
        const status = video.readyState >= 4 ? '✅ 可播放' : 
                      video.readyState >= 1 ? '⏳ 加载中' : '❌ 未加载';
        
        info += `视频${index + 1}: ${resolution} ${status}\n`;
        
        if (video.src) {
            const urlParts = video.src.split('/');
            const filename = urlParts[urlParts.length - 1];
            info += `文件: ${filename}\n`;
        }
        info += '\n';
    });
    
    info += `🔧 当前URL格式: ${window.UltimateVideoConfig.currentFormatIndex}\n`;
    info += `⏰ 更新时间: ${new Date().toLocaleTimeString()}`;
    
    infoDiv.textContent = info;
}

// 修复视频元素
async function fixVideoElement(video, pageKey) {
    console.log('🔧 修复视频元素:', video.src);
    
    // 查找可用的URL
    const workingUrl = await findWorkingUrl(pageKey);
    if (!workingUrl) {
        console.error('❌ 无法找到可用的视频URL');
        return false;
    }
    
    // 更新视频源
    video.src = workingUrl;
    
    // 添加事件监听
    video.addEventListener('loadedmetadata', () => {
        console.log(`✅ 视频元数据加载完成: ${video.videoWidth}x${video.videoHeight}`);
        updateVideoInfoDisplay();
    });
    
    video.addEventListener('canplaythrough', () => {
        console.log('✅ 视频可以播放');
        updateVideoInfoDisplay();
    });
    
    video.addEventListener('error', (e) => {
        console.error('❌ 视频加载错误:', e);
        updateVideoInfoDisplay();
    });
    
    // 重新加载视频
    video.load();
    
    return true;
}

// 获取当前页面类型
function getCurrentPageKey() {
    const path = window.location.pathname;
    const pathMap = {
        '/': 'INDEX',
        '/index.html': 'INDEX',
        '/meetings': 'MEETINGS',
        '/meetings.html': 'MEETINGS',
        '/anniversary': 'ANNIVERSARY',
        '/anniversary.html': 'ANNIVERSARY',
        '/memorial': 'MEMORIAL',
        '/memorial.html': 'MEMORIAL',
        '/together-days': 'TOGETHER_DAYS',
        '/together-days.html': 'TOGETHER_DAYS'
    };
    
    return pathMap[path] || 'INDEX';
}

// 终极视频修复函数
async function ultimateVideoFix() {
    console.log('🚀 启动终极视频修复...');
    
    // 创建信息显示
    createVideoInfoDisplay();
    
    // 获取当前页面
    const pageKey = getCurrentPageKey();
    console.log('📄 当前页面:', pageKey);
    
    // 查找所有视频元素
    const videos = document.querySelectorAll('video');
    console.log(`🎥 找到 ${videos.length} 个视频元素`);
    
    if (videos.length === 0) {
        console.warn('⚠️ 未找到视频元素');
        return;
    }
    
    // 修复每个视频
    for (const video of videos) {
        await fixVideoElement(video, pageKey);
    }
    
    // 定期更新信息显示
    setInterval(updateVideoInfoDisplay, 2000);
    
    console.log('✅ 终极视频修复完成');
}

// 手动切换URL格式
function switchUrlFormat(formatIndex) {
    if (formatIndex >= 0 && formatIndex < window.UltimateVideoConfig.urlFormats.length) {
        window.UltimateVideoConfig.currentFormatIndex = formatIndex;
        console.log(`🔄 切换到URL格式 ${formatIndex}`);
        ultimateVideoFix();
    } else {
        console.error('❌ 无效的格式索引:', formatIndex);
    }
}

// 切换信息显示
function toggleVideoInfo() {
    window.UltimateVideoConfig.showVideoInfo = !window.UltimateVideoConfig.showVideoInfo;
    
    if (window.UltimateVideoConfig.showVideoInfo) {
        createVideoInfoDisplay();
        updateVideoInfoDisplay();
    } else {
        const infoDiv = document.getElementById('video-info-display');
        if (infoDiv) infoDiv.remove();
    }
}

// 导出全局函数
window.ultimateVideoFix = ultimateVideoFix;
window.switchUrlFormat = switchUrlFormat;
window.toggleVideoInfo = toggleVideoInfo;
window.updateVideoInfoDisplay = updateVideoInfoDisplay;

// 页面加载完成后自动执行
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', ultimateVideoFix);
} else {
    ultimateVideoFix();
}

console.log('🎯 终极视频修复脚本已加载');
console.log('💡 可用命令: ultimateVideoFix(), switchUrlFormat(0-4), toggleVideoInfo()');
