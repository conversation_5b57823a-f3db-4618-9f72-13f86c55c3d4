#!/bin/bash

# 更新所有HTML页面的视频加载方式
# 添加新的CDN管理器支持

echo "🔄 开始更新所有页面的视频加载方式..."

# HTML页面列表
pages=(
    "html/index.html"
    "html/anniversary.html" 
    "html/meetings.html"
    "html/memorial.html"
    "html/together-days.html"
)

# 备份原文件
echo "📁 创建备份..."
for page in "${pages[@]}"; do
    if [ -f "$page" ]; then
        cp "$page" "${page}.backup-$(date +%Y%m%d-%H%M%S)"
        echo "✅ 备份: $page"
    fi
done

echo "🔧 更新页面脚本引用..."

# 更新每个页面
for page in "${pages[@]}"; do
    if [ -f "$page" ]; then
        echo "📝 更新: $page"
        
        # 检查是否已经包含新的脚本
        if ! grep -q "hybrid-cdn-manager.js" "$page"; then
            # 在cloudinary-setup.js之后添加新脚本
            sed -i 's|<script src="/cloudinary-setup.js"></script>|<script src="/cloudinary-setup.js"></script>\n    <script src="/hybrid-cdn-manager.js"></script>|g' "$page"
            echo "  ✅ 添加了hybrid-cdn-manager.js"
        fi
        
        if ! grep -q "cloudinary-load-balancer.js" "$page"; then
            # 添加负载均衡器
            sed -i 's|<script src="/hybrid-cdn-manager.js"></script>|<script src="/hybrid-cdn-manager.js"></script>\n    <script src="/cloudinary-load-balancer.js"></script>|g' "$page"
            echo "  ✅ 添加了cloudinary-load-balancer.js"
        fi
        
    else
        echo "❌ 文件不存在: $page"
    fi
done

echo "✅ 所有页面更新完成！"
echo "📋 更新内容："
echo "  - 添加了混合CDN管理器支持"
echo "  - 添加了负载均衡器支持"
echo "  - 保留了原有的视频管理器兼容性"
echo ""
echo "🎯 下一步："
echo "  1. 测试页面加载: https://love.yuh.cool/"
echo "  2. 查看CDN状态: https://love.yuh.cool/test/test-cloudinary-integration.html"
echo "  3. 监控性能表现"
