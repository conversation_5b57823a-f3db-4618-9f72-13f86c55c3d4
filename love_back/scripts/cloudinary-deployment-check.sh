#!/bin/bash

# Cloudinary部署检查脚本
# 验证所有配置和文件是否正确设置

echo "🔍 Cloudinary部署检查开始..."

# 检查必要文件
echo "📁 检查必要文件..."
files=(
    "cloudinary-setup.js"
    "scripts/compress-videos-for-cloudinary.sh"
    "scripts/upload-to-cloudinary.js"
    "background/cloudinary-ready"
)

for file in "${files[@]}"; do
    if [ -e "$file" ]; then
        echo "✅ $file - 存在"
    else
        echo "❌ $file - 缺失"
    fi
done

# 检查Node.js依赖
echo -e "\n📦 检查Node.js依赖..."
if command -v npm &> /dev/null; then
    echo "✅ npm - 已安装"
    if npm list cloudinary &> /dev/null; then
        echo "✅ cloudinary包 - 已安装"
    else
        echo "❌ cloudinary包 - 未安装"
        echo "💡 运行: npm install cloudinary"
    fi
else
    echo "❌ npm - 未安装"
fi

# 检查环境变量
echo -e "\n🔧 检查环境变量..."
env_vars=("CLOUDINARY_CLOUD_NAME" "CLOUDINARY_API_KEY" "CLOUDINARY_API_SECRET")

for var in "${env_vars[@]}"; do
    if [ -n "${!var}" ]; then
        echo "✅ $var - 已设置"
    else
        echo "❌ $var - 未设置"
    fi
done

# 检查视频文件大小
echo -e "\n📊 检查视频文件大小..."
if [ -d "background/cloudinary-ready" ]; then
    for video in background/cloudinary-ready/*.mp4; do
        if [ -f "$video" ]; then
            size=$(du -m "$video" | cut -f1)
            filename=$(basename "$video")
            if [ "$size" -le 100 ]; then
                echo "✅ $filename - ${size}MB (符合免费计划)"
            else
                echo "❌ $filename - ${size}MB (超出100MB限制)"
            fi
        fi
    done
else
    echo "❌ background/cloudinary-ready目录不存在"
    echo "💡 运行: ./scripts/compress-videos-for-cloudinary.sh"
fi

# 检查配置文件
echo -e "\n⚙️ 检查配置文件..."
if grep -q "CLOUDINARY" config.js; then
    echo "✅ config.js - 包含Cloudinary配置"
    if grep -q "ENABLED: true" config.js; then
        echo "✅ Cloudinary - 已启用"
    else
        echo "⚠️ Cloudinary - 未启用 (ENABLED: false)"
    fi
else
    echo "❌ config.js - 缺少Cloudinary配置"
fi

echo -e "\n🎯 部署检查完成！"
echo "📋 下一步操作指南："
echo "1. 如有缺失文件，请重新创建"
echo "2. 安装依赖: npm install cloudinary"
echo "3. 设置环境变量"
echo "4. 压缩视频: ./scripts/compress-videos-for-cloudinary.sh"
echo "5. 上传视频: node scripts/upload-to-cloudinary.js"
echo "6. 启用Cloudinary: 在config.js中设置ENABLED: true"
