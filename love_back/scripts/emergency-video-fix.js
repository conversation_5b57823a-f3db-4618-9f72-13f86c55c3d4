/**
 * 紧急视频修复脚本
 * 解决CDN管理器初始化失败和Cloudinary URL错误问题
 */

(function() {
    'use strict';
    
    console.log('🚨 紧急视频修复脚本启动...');
    
    // 修复后的视频URL生成函数 - 使用简化的稳定格式
    function generateWorkingVideoUrl(pageKey) {
        // 直接使用工作的Cloudinary URL，绕过复杂的CDN管理器
        const videoConfigs = {
            'INDEX': {
                cloudName: 'dcglebc2w',
                publicId: 'love-website/home'
            },
            'MEETINGS': {
                cloudName: 'dkqnm9nwr',
                publicId: 'love-website/meetings'
            },
            'ANNIVERSARY': {
                cloudName: 'drhqbbqxz',
                publicId: 'love-website/anniversary'
            },
            'MEMORIAL': {
                cloudName: 'ds14sv2gh',
                publicId: 'love-website/memorial'
            },
            'TOGETHER_DAYS': {
                cloudName: 'dpq95x5nf',
                publicId: 'love-website/together-days'
            }
        };

        const config = videoConfigs[pageKey];
        if (!config) {
            console.error(`❌ [EmergencyFix] 未找到页面 ${pageKey} 的配置`);
            return null;
        }

        // 使用统一的设备检测逻辑（与DeviceDetectionUtils保持一致）
        const screenWidth = window.innerWidth;
        const userAgent = navigator.userAgent;
        const isMobileUA = /Mobile|Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
        const isTabletUA = /iPad|Android.*Tablet|Windows.*Touch/i.test(userAgent) && !/Mobile/i.test(userAgent);

        // 统一的设备检测逻辑
        const isMobile = (screenWidth <= 480) || (screenWidth <= 768 && isMobileUA && !isTabletUA);
        const isTablet = (screenWidth > 480 && screenWidth <= 1024 && isTabletUA) ||
                        (screenWidth > 768 && screenWidth <= 1024 && !isMobile);

        // 使用最简化的变换参数（彻底修复400错误）
        let transforms;
        if (isMobile) {
            transforms = 'c_scale,w_1920';
            console.log(`📱 [EmergencyFix] ${pageKey}: 使用移动设备高清质量 (1920宽度)`);
        } else if (isTablet) {
            transforms = 'c_scale,w_2560';
            console.log(`📱 [EmergencyFix] ${pageKey}: 使用平板设备2K质量 (2560宽度)`);
        } else {
            transforms = 'c_scale,w_2560';
            console.log(`🖥️ [EmergencyFix] ${pageKey}: 使用桌面设备2K质量 (2560宽度)`);
        }

        const url = `https://res.cloudinary.com/${config.cloudName}/video/upload/${transforms}/${config.publicId}.mp4`;
        console.log(`📹 [EmergencyFix] 生成主URL: ${url}`);

        return url;
    }

    // 备用URL生成函数 - 无变换参数的原始URL
    function generateFallbackVideoUrl(pageKey) {
        const videoConfigs = {
            'INDEX': {
                cloudName: 'dcglebc2w',
                publicId: 'love-website/home'
            },
            'MEETINGS': {
                cloudName: 'dkqnm9nwr',
                publicId: 'love-website/meetings'
            },
            'ANNIVERSARY': {
                cloudName: 'drhqbbqxz',
                publicId: 'love-website/anniversary'
            },
            'MEMORIAL': {
                cloudName: 'ds14sv2gh',
                publicId: 'love-website/memorial'
            },
            'TOGETHER_DAYS': {
                cloudName: 'dpq95x5nf',
                publicId: 'love-website/together-days'
            }
        };

        const config = videoConfigs[pageKey];
        if (!config) {
            console.error(`❌ [EmergencyFix] 未找到页面 ${pageKey} 的备用配置`);
            return null;
        }

        // 返回无变换参数的原始URL
        const fallbackUrl = `https://res.cloudinary.com/${config.cloudName}/video/upload/${config.publicId}.mp4`;
        console.log(`📹 [EmergencyFix] 生成备用URL: ${fallbackUrl}`);

        return fallbackUrl;
    }
    
    // 创建简化的VideoManager
    function createEmergencyVideoManager() {
        return {
            isInitialized: true,
            
            async loadVideo(pageKey, videoConfig) {
                console.log(`🎬 [EmergencyFix] 紧急加载视频: ${pageKey}`);

                const video = document.createElement('video');
                video.autoplay = true;
                video.muted = true;
                video.loop = true;
                video.playsInline = true;
                video.preload = 'auto';
                video.style.opacity = '0';
                video.style.transition = 'opacity 1s ease-in-out';

                const primaryUrl = generateWorkingVideoUrl(pageKey);
                const fallbackUrl = generateFallbackVideoUrl(pageKey);

                if (!primaryUrl) {
                    throw new Error(`无法生成${pageKey}的视频URL`);
                }

                return new Promise((resolve, reject) => {
                    let attemptCount = 0;
                    const startTime = performance.now();

                    const timeout = setTimeout(() => {
                        console.warn(`⚠️ [EmergencyFix] ${pageKey}视频加载超时 (15秒)`);
                        if (fallbackUrl && video.src !== fallbackUrl) {
                            console.log(`🔄 [EmergencyFix] 超时后尝试备用URL: ${fallbackUrl}`);
                            video.src = fallbackUrl;
                            video.load();
                        } else {
                            reject(new Error('视频加载超时'));
                        }
                    }, 15000);

                    video.addEventListener('canplaythrough', () => {
                        clearTimeout(timeout);
                        const loadTime = performance.now() - startTime;

                        video.style.opacity = '1';
                        console.log(`✅ [EmergencyFix] ${pageKey}视频加载成功 - 耗时: ${loadTime.toFixed(0)}ms, 尝试次数: ${attemptCount + 1}, URL: ${video.src}`);
                        resolve(video);
                    });

                    video.addEventListener('error', (e) => {
                        attemptCount++;
                        const loadTime = performance.now() - startTime;

                        console.error(`❌ [EmergencyFix] ${pageKey}视频加载失败 (尝试 ${attemptCount}):`, {
                            error: e.message || e.type || '未知错误',
                            currentUrl: video.src,
                            loadTime: loadTime
                        });

                        // 如果是主URL失败且有备用URL，自动切换
                        if (video.src === primaryUrl && fallbackUrl) {
                            console.log(`🔄 [EmergencyFix] 主URL失败，自动切换到备用URL: ${fallbackUrl}`);
                            video.src = fallbackUrl;
                            video.load();
                            return; // 不要清除超时，给备用URL一个机会
                        }

                        // 如果备用URL也失败，或没有备用URL
                        clearTimeout(timeout);
                        console.error(`❌ [EmergencyFix] 所有URL都失败，${pageKey}视频加载彻底失败`);
                        reject(new Error(`视频加载失败: ${e.message || '未知错误'}`));
                    });

                    // 开始加载主URL
                    console.log(`🚀 [EmergencyFix] 开始加载主URL: ${primaryUrl}`);
                    video.src = primaryUrl;
                    video.load();
                });
            },
            
            pauseAllVideos() {
                const videos = document.querySelectorAll('video');
                videos.forEach(video => {
                    if (!video.paused) {
                        video.pause();
                    }
                });
            },
            
            resumeCurrentVideo() {
                const videos = document.querySelectorAll('video');
                videos.forEach(video => {
                    if (video.paused) {
                        video.play().catch(e => {
                            console.log('自动播放被阻止');
                        });
                    }
                });
            },
            
            startPreloading() {
                console.log('📦 预加载功能已禁用（紧急模式）');
                return Promise.resolve();
            },
            
            cleanupPreloadCache() {
                console.log('🗑️ 缓存清理功能已禁用（紧急模式）');
            }
        };
    }
    
    // 立即修复当前页面的视频 - 支持双重URL故障转移
    function fixCurrentPageVideo() {
        const videoElements = document.querySelectorAll('video');

        if (videoElements.length === 0) {
            console.log('⚠️ [EmergencyFix] 未找到视频元素');
            return;
        }

        const pageKey = getCurrentPageKey();
        console.log(`📄 [EmergencyFix] 当前页面: ${pageKey}`);

        const primaryUrl = generateWorkingVideoUrl(pageKey);
        const fallbackUrl = generateFallbackVideoUrl(pageKey);

        if (!primaryUrl) {
            console.error('❌ [EmergencyFix] 无法生成正确的视频URL');
            return;
        }

        console.log(`📹 [EmergencyFix] 主URL: ${primaryUrl}`);
        console.log(`📹 [EmergencyFix] 备用URL: ${fallbackUrl}`);

        videoElements.forEach((video, index) => {
            if (video.src !== primaryUrl && video.src !== fallbackUrl) {
                console.log(`🔄 [EmergencyFix] 修复视频 ${index + 1}/${videoElements.length}:`);
                console.log(`   原URL: ${video.src}`);
                console.log(`   新URL: ${primaryUrl}`);

                // 设置加载超时
                const loadTimeout = setTimeout(() => {
                    if (fallbackUrl && video.src !== fallbackUrl) {
                        console.log(`🔄 [EmergencyFix] 视频 ${index + 1} 主URL超时，尝试备用URL: ${fallbackUrl}`);
                        video.src = fallbackUrl;
                        video.load();
                    }
                }, 10000);

                video.addEventListener('canplaythrough', () => {
                    clearTimeout(loadTimeout);
                    video.style.opacity = '1';
                    console.log(`✅ [EmergencyFix] 视频 ${index + 1} 加载成功: ${video.src}`);

                    video.play().catch(e => {
                        console.log('🔇 [EmergencyFix] 自动播放被阻止，等待用户交互');
                    });
                });

                video.addEventListener('error', (e) => {
                    console.error(`❌ [EmergencyFix] 视频 ${index + 1} 加载失败:`, e.message || e.type);

                    // 如果是主URL失败且有备用URL，自动切换
                    if (video.src === primaryUrl && fallbackUrl) {
                        console.log(`🔄 [EmergencyFix] 视频 ${index + 1} 主URL失败，自动切换到备用URL: ${fallbackUrl}`);
                        video.src = fallbackUrl;
                        video.load();
                    } else {
                        clearTimeout(loadTimeout);
                        console.error(`❌ [EmergencyFix] 视频 ${index + 1} 所有URL都失败`);
                    }
                });

                // 开始加载主URL
                video.src = primaryUrl;
                video.load();

                console.log(`🚀 [EmergencyFix] 视频 ${index + 1} 开始加载主URL`);
            } else {
                console.log(`✅ [EmergencyFix] 视频 ${index + 1} 已使用正确URL，无需修复`);
            }
        });
    }
    
    // 状态检查函数
    function checkEmergencyFixStatus() {
        const status = {
            timestamp: new Date().toISOString(),
            page: getCurrentPageKey(),
            videoManager: {
                available: !!window.VideoManager,
                isEmergency: window.VideoManager && window.VideoManager.isEmergency,
                initialized: window.VideoManager && window.VideoManager.isInitialized
            },
            functions: {
                emergencyVideoFix: typeof window.emergencyVideoFix === 'function',
                generateWorkingVideoUrl: typeof window.generateWorkingVideoUrl === 'function',
                generateFallbackVideoUrl: typeof window.generateFallbackVideoUrl === 'function'
            },
            videos: {
                count: document.querySelectorAll('video').length,
                sources: Array.from(document.querySelectorAll('video')).map(v => v.src)
            },
            device: {
                screenWidth: window.innerWidth,
                screenHeight: window.innerHeight,
                userAgent: navigator.userAgent,
                isMobile: (window.innerWidth <= 480) || (window.innerWidth <= 768 && /Mobile/i.test(navigator.userAgent))
            }
        };

        console.log('📊 [EmergencyFix] 状态检查结果:', status);
        return status;
    }

    // 获取当前页面键
    function getCurrentPageKey() {
        const path = window.location.pathname;
        if (path.includes('meetings')) return 'MEETINGS';
        if (path.includes('anniversary')) return 'ANNIVERSARY';
        if (path.includes('memorial')) return 'MEMORIAL';
        if (path.includes('together-days')) return 'TOGETHER_DAYS';
        return 'INDEX';
    }

    // 主修复函数
    function applyEmergencyFix() {
        console.log('🚨 [EmergencyFix] 应用紧急修复...');

        // 1. 创建紧急VideoManager
        if (!window.VideoManager || !window.VideoManager.isInitialized) {
            console.log('🔧 [EmergencyFix] 创建紧急VideoManager...');
            const emergencyManager = createEmergencyVideoManager();
            emergencyManager.isEmergency = true; // 标记为紧急模式
            window.VideoManager = emergencyManager;

            // 触发就绪事件
            window.dispatchEvent(new CustomEvent('videoManagerReady', {
                detail: { videoManager: window.VideoManager, isEmergency: true }
            }));

            console.log('✅ [EmergencyFix] 紧急VideoManager已创建');
        }

        // 2. 修复当前页面视频
        setTimeout(() => {
            fixCurrentPageVideo();
        }, 1000);

        // 3. 提供全局修复函数
        window.emergencyVideoFix = fixCurrentPageVideo;
        window.generateWorkingVideoUrl = generateWorkingVideoUrl;
        window.generateFallbackVideoUrl = generateFallbackVideoUrl;
        window.checkEmergencyFixStatus = checkEmergencyFixStatus;
        window.getCurrentPageKey = getCurrentPageKey;

        // 4. 提供批量修复函数
        window.fixAllVideos = function() {
            console.log('🔧 [EmergencyFix] 批量修复所有视频...');
            const videos = document.querySelectorAll('video');
            const pageKey = getCurrentPageKey();
            const primaryUrl = generateWorkingVideoUrl(pageKey);
            const fallbackUrl = generateFallbackVideoUrl(pageKey);

            if (!primaryUrl) {
                console.error('❌ [EmergencyFix] 无法生成视频URL');
                return;
            }

            videos.forEach((video, index) => {
                console.log(`🔄 [EmergencyFix] 修复视频 ${index + 1}/${videos.length}`);
                video.src = primaryUrl;
                video.load();

                video.addEventListener('error', () => {
                    if (fallbackUrl) {
                        console.log(`🔄 [EmergencyFix] 视频 ${index + 1} 主URL失败，尝试备用URL`);
                        video.src = fallbackUrl;
                        video.load();
                    }
                });
            });
        };

        console.log('✅ [EmergencyFix] 紧急修复已应用');
        console.log('💡 [EmergencyFix] 可用命令:');
        console.log('  - emergencyVideoFix(): 手动修复当前页面视频');
        console.log('  - fixAllVideos(): 批量修复所有视频元素');
        console.log('  - generateWorkingVideoUrl("INDEX"): 生成指定页面的主URL');
        console.log('  - generateFallbackVideoUrl("INDEX"): 生成指定页面的备用URL');
        console.log('  - checkEmergencyFixStatus(): 检查修复状态');
        console.log('  - getCurrentPageKey(): 获取当前页面键');
    }
    
    // 立即应用核心修复（在页面加载最早期）
    console.log('⚡ [EmergencyFix] 立即应用核心修复...');

    // 1. 立即创建紧急VideoManager（如果不存在）
    if (!window.VideoManager || !window.VideoManager.isInitialized) {
        const emergencyManager = createEmergencyVideoManager();
        emergencyManager.isEmergency = true;
        window.VideoManager = emergencyManager;
        console.log('⚡ [EmergencyFix] 立即创建紧急VideoManager');
    }

    // 2. 立即提供全局函数
    window.emergencyVideoFix = fixCurrentPageVideo;
    window.generateWorkingVideoUrl = generateWorkingVideoUrl;
    window.generateFallbackVideoUrl = generateFallbackVideoUrl;
    window.checkEmergencyFixStatus = checkEmergencyFixStatus;
    window.getCurrentPageKey = getCurrentPageKey;

    // 3. 根据DOM状态应用完整修复
    if (document.readyState === 'loading') {
        // DOM还在加载中，等待完成
        document.addEventListener('DOMContentLoaded', () => {
            console.log('📄 [EmergencyFix] DOM加载完成，应用完整修复');
            applyEmergencyFix();
        });

        // 也监听更早的事件
        document.addEventListener('readystatechange', () => {
            if (document.readyState === 'interactive') {
                console.log('📄 [EmergencyFix] DOM交互就绪，尝试早期修复');
                setTimeout(fixCurrentPageVideo, 500);
            }
        });
    } else if (document.readyState === 'interactive') {
        // DOM已经可交互，稍等一下再修复
        console.log('📄 [EmergencyFix] DOM已交互就绪，延迟应用修复');
        setTimeout(applyEmergencyFix, 100);
    } else {
        // DOM已完全加载，立即修复
        console.log('📄 [EmergencyFix] DOM已完全加载，立即应用修复');
        applyEmergencyFix();
    }

    // 4. 监听页面可见性变化，确保视频在页面重新可见时正常工作
    if (typeof document !== 'undefined') {
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden) {
                console.log('👁️ [EmergencyFix] 页面重新可见，检查视频状态');
                setTimeout(() => {
                    const videos = document.querySelectorAll('video');
                    videos.forEach(video => {
                        if (video.paused && video.autoplay) {
                            video.play().catch(e => {
                                console.log('🔇 [EmergencyFix] 自动播放被阻止');
                            });
                        }
                    });
                }, 1000);
            }
        });
    }

    console.log('✅ [EmergencyFix] 紧急修复脚本初始化完成');
    console.log('💡 [EmergencyFix] 可在控制台使用 emergencyVideoFix() 手动修复视频');
    
})();
