#!/usr/bin/env node

/**
 * 调试视频播放问题
 * 全面检查为什么视频没有播放，显示紫色背景
 */

console.log('🔍 调试视频播放问题');
console.log('=====================================\n');

// 生成浏览器调试代码
function generateBrowserDebugCode() {
    console.log('📋 浏览器调试代码 - 在love.yuh.cool控制台执行:');
    console.log('=====================================');
    
    const debugCode = `
// 全面调试视频播放问题
function debugVideoIssue() {
    console.log('🔍 开始全面调试视频播放问题...');
    console.log('=====================================');
    
    // 1. 检查视频配置
    console.log('📋 1. 检查视频配置:');
    console.log('VIDEO_CONFIG:', window.VIDEO_CONFIG);
    
    // 2. 检查视频容器
    console.log('\\n📦 2. 检查视频容器:');
    const container = document.getElementById('video-background-container');
    console.log('容器存在:', !!container);
    if (container) {
        console.log('容器类名:', container.className);
        console.log('容器子元素数量:', container.children.length);
        console.log('容器样式:', {
            display: getComputedStyle(container).display,
            position: getComputedStyle(container).position,
            zIndex: getComputedStyle(container).zIndex
        });
    }
    
    // 3. 检查视频元素
    console.log('\\n🎥 3. 检查视频元素:');
    const videos = document.querySelectorAll('video');
    console.log('视频元素数量:', videos.length);
    
    videos.forEach((video, index) => {
        console.log(\`\\n视频\${index + 1}详细信息:\`);
        console.log('  ID:', video.id);
        console.log('  URL:', video.src);
        console.log('  自动播放:', video.autoplay);
        console.log('  静音:', video.muted);
        console.log('  循环:', video.loop);
        console.log('  预加载:', video.preload);
        console.log('  playsInline:', video.playsInline);
        console.log('  readyState:', video.readyState, '(4=可播放)');
        console.log('  networkState:', video.networkState);
        console.log('  paused:', video.paused);
        console.log('  ended:', video.ended);
        console.log('  currentTime:', video.currentTime);
        console.log('  duration:', video.duration);
        console.log('  分辨率:', \`\${video.videoWidth}x\${video.videoHeight}\`);
        console.log('  样式:', {
            display: getComputedStyle(video).display,
            position: getComputedStyle(video).position,
            zIndex: getComputedStyle(video).zIndex,
            opacity: getComputedStyle(video).opacity,
            visibility: getComputedStyle(video).visibility
        });
    });
    
    // 4. 检查加载遮罩
    console.log('\\n🎭 4. 检查加载遮罩:');
    const loadingOverlay = document.getElementById('loadingOverlay');
    console.log('加载遮罩存在:', !!loadingOverlay);
    if (loadingOverlay) {
        console.log('是否隐藏:', loadingOverlay.classList.contains('hidden'));
        console.log('显示状态:', getComputedStyle(loadingOverlay).display);
        console.log('透明度:', getComputedStyle(loadingOverlay).opacity);
    }
    
    // 5. 检查背景样式
    console.log('\\n🎨 5. 检查背景样式:');
    const body = document.body;
    const html = document.documentElement;
    console.log('body背景:', getComputedStyle(body).background);
    console.log('html背景:', getComputedStyle(html).background);
    
    // 6. 检查控制台错误
    console.log('\\n❌ 6. 如果有JavaScript错误，请查看上方的红色错误信息');
    
    console.log('\\n=====================================');
    console.log('🔍 调试完成，请查看上述信息找出问题');
}

// 强制创建和播放视频
function forceCreateAndPlayVideo() {
    console.log('🔄 强制创建和播放视频...');
    
    // 移除现有视频
    const existingVideos = document.querySelectorAll('video');
    existingVideos.forEach(video => video.remove());
    
    // 获取容器
    const container = document.getElementById('video-background-container');
    if (!container) {
        console.error('❌ 视频容器不存在，无法创建视频');
        return;
    }
    
    // 创建新视频元素
    const video = document.createElement('video');
    video.id = 'background-video-forced';
    video.muted = true;  // 确保静音
    video.loop = true;
    video.playsInline = true;
    video.preload = 'auto';
    video.controls = false;
    
    // 设置样式
    video.style.cssText = \`
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
        z-index: -1;
        opacity: 1;
    \`;
    
    // 监听事件
    video.addEventListener('loadedmetadata', function() {
        console.log(\`📊 强制视频分辨率: \${this.videoWidth}x\${this.videoHeight}\`);
        
        // 尝试播放
        this.play().then(() => {
            console.log('✅ 视频播放成功！');
            // 隐藏加载遮罩
            const loadingOverlay = document.getElementById('loadingOverlay');
            if (loadingOverlay) {
                loadingOverlay.classList.add('hidden');
            }
        }).catch(error => {
            console.error('❌ 视频播放失败:', error);
            console.log('💡 可能需要用户交互才能播放视频');
        });
    });
    
    video.addEventListener('error', function(e) {
        console.error('❌ 强制视频加载失败:', e);
        console.log('错误详情:', {
            error: e.target.error,
            code: e.target.error ? e.target.error.code : 'unknown',
            message: e.target.error ? e.target.error.message : 'unknown'
        });
    });
    
    video.addEventListener('canplay', function() {
        console.log('📹 视频可以播放');
    });
    
    video.addEventListener('canplaythrough', function() {
        console.log('📹 视频可以流畅播放');
    });
    
    // 设置视频源
    video.src = '/background/home/<USER>';
    container.appendChild(video);
    
    console.log('🎬 强制视频已创建，正在加载...');
    
    return video;
}

// 检查视频文件可访问性
async function checkVideoFileAccess() {
    console.log('🔍 检查视频文件可访问性...');
    
    const urls = [
        '/background/home/<USER>',
        '/background/home/<USER>'
    ];
    
    for (const url of urls) {
        try {
            const response = await fetch(url, { method: 'HEAD' });
            const contentLength = response.headers.get('content-length');
            const contentType = response.headers.get('content-type');
            const sizeMB = contentLength ? (parseInt(contentLength) / 1024 / 1024).toFixed(2) + 'MB' : 'Unknown';
            
            console.log(\`\${response.ok ? '✅' : '❌'} \${url}:\`);
            console.log(\`  状态: \${response.status}\`);
            console.log(\`  大小: \${sizeMB}\`);
            console.log(\`  类型: \${contentType}\`);
        } catch (error) {
            console.log(\`❌ \${url}: \${error.message}\`);
        }
    }
}

// 用户交互触发播放
function enableUserInteractionPlay() {
    console.log('👆 设置用户交互触发播放...');
    
    const clickHandler = function() {
        console.log('👆 用户点击，尝试播放视频...');
        
        const videos = document.querySelectorAll('video');
        videos.forEach(video => {
            if (video.paused) {
                video.play().then(() => {
                    console.log('✅ 用户交互后视频播放成功！');
                }).catch(error => {
                    console.error('❌ 用户交互后视频播放仍然失败:', error);
                });
            }
        });
        
        // 移除事件监听器
        document.removeEventListener('click', clickHandler);
        console.log('👆 用户交互监听器已移除');
    };
    
    document.addEventListener('click', clickHandler);
    console.log('👆 请点击页面任意位置来触发视频播放');
}

// 立即执行调试
debugVideoIssue();
checkVideoFileAccess();

console.log('\\n💡 如果视频仍然不播放，请尝试:');
console.log('1. forceCreateAndPlayVideo() - 强制创建视频');
console.log('2. enableUserInteractionPlay() - 启用用户交互播放');
`;
    
    console.log(debugCode);
    console.log('=====================================\n');
}

// 主函数
function runVideoDebug() {
    console.log('🧪 开始调试视频播放问题...\n');
    
    generateBrowserDebugCode();
    
    console.log('📝 可能的问题和解决方案:');
    console.log('1. 🚫 浏览器自动播放策略阻止 - 需要用户交互');
    console.log('2. 📁 视频文件路径错误 - 检查文件是否存在');
    console.log('3. 🎥 视频元素创建失败 - 检查JavaScript错误');
    console.log('4. 🎭 加载遮罩没有隐藏 - 检查事件监听器');
    console.log('5. 🎨 CSS样式问题 - 检查z-index和opacity');
    
    console.log('\n🚀 下一步操作:');
    console.log('1. 访问 love.yuh.cool');
    console.log('2. 打开浏览器开发者工具 (F12)');
    console.log('3. 在控制台执行上述调试代码');
    console.log('4. 根据调试结果确定问题原因');
    console.log('5. 尝试强制创建视频或用户交互播放');
    
    console.log('\n✅ 视频调试脚本准备完成！');
}

// 执行调试
if (require.main === module) {
    runVideoDebug();
}

module.exports = { runVideoDebug };
