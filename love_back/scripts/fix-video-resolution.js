/**
 * 视频分辨率问题修复脚本
 * 
 * 此脚本用于诊断和修复Love Website中视频背景分辨率显示为手机分辨率的问题
 * 
 * 使用方法：
 * 1. 在浏览器控制台中运行此脚本
 * 2. 或者在页面中添加 <script src="/scripts/fix-video-resolution.js"></script>
 */

(function() {
    'use strict';
    
    console.log('🔧 视频分辨率修复脚本启动...');
    
    // 修复后的设备检测函数
    function getImprovedDeviceDetection() {
        const screenWidth = window.innerWidth;
        const screenHeight = window.innerHeight;
        const userAgent = navigator.userAgent;
        
        // 更准确的User Agent检测
        const isMobileUA = /Mobile|Android|iPhone|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
        const isTabletUA = /iPad|Android.*Tablet|Windows.*Touch/i.test(userAgent) && !/Mobile/i.test(userAgent);
        const isDesktopUA = !isMobileUA && !isTabletUA;
        
        // 改进的设备类型判断
        let deviceType = 'desktop';
        let isMobile = false;
        let isTablet = false;
        
        // 真正的移动设备：屏幕小且User Agent匹配
        if (screenWidth <= 480 || (screenWidth <= 768 && isMobileUA && !isTabletUA)) {
            deviceType = 'mobile';
            isMobile = true;
        }
        // 平板设备：中等屏幕尺寸
        else if ((screenWidth > 480 && screenWidth <= 1024 && isTabletUA) || 
                 (screenWidth > 768 && screenWidth <= 1024 && !isMobile)) {
            deviceType = 'tablet';
            isTablet = true;
        }
        // 桌面设备：大屏幕或明确的桌面User Agent
        else {
            deviceType = 'desktop';
        }
        
        return {
            deviceType,
            isMobile,
            isTablet,
            screenWidth,
            screenHeight,
            userAgent,
            isMobileUA,
            isTabletUA,
            isDesktopUA
        };
    }
    
    // 生成正确的Cloudinary URL
    function generateCorrectCloudinaryUrl(pageName, deviceInfo) {
        const baseUrls = {
            'INDEX': 'https://res.cloudinary.com/dcglebc2w/video/upload',
            'MEETINGS': 'https://res.cloudinary.com/dkqnm9nwr/video/upload', 
            'ANNIVERSARY': 'https://res.cloudinary.com/drhqbbqxz/video/upload',
            'MEMORIAL': 'https://res.cloudinary.com/ds14sv2gh/video/upload',
            'TOGETHER_DAYS': 'https://res.cloudinary.com/dpq95x5nf/video/upload'
        };
        
        const publicIds = {
            'INDEX': 'love-website/home',
            'MEETINGS': 'love-website/meetings',
            'ANNIVERSARY': 'love-website/anniversary', 
            'MEMORIAL': 'love-website/memorial',
            'TOGETHER_DAYS': 'love-website/together-days'
        };
        
        const baseUrl = baseUrls[pageName];
        const publicId = publicIds[pageName];
        
        if (!baseUrl || !publicId) {
            console.error(`❌ 未找到页面 ${pageName} 的配置`);
            return null;
        }
        
        // 根据设备类型选择合适的变换参数
        let transforms;
        
        if (deviceInfo.isMobile) {
            // 移动设备使用1080p高清
            transforms = 'q_95,f_auto,w_1920,h_1080,c_limit,fl_immutable_cache';
            console.log(`📱 ${pageName}: 使用移动设备高清质量 (1920x1080)`);
        } else if (deviceInfo.isTablet) {
            // 平板设备使用2K
            transforms = 'q_100,f_auto,w_2560,h_1440,c_limit,fl_immutable_cache';
            console.log(`📱 ${pageName}: 使用平板设备2K质量 (2560x1440)`);
        } else {
            // 桌面设备使用2K无损
            transforms = 'q_100,f_auto,w_2560,h_1440,c_limit,fl_immutable_cache';
            console.log(`🖥️ ${pageName}: 使用桌面设备2K无损质量 (2560x1440)`);
        }
        
        return `${baseUrl}/${transforms}/${publicId}.mp4`;
    }
    
    // 修复当前页面的视频
    function fixCurrentPageVideo() {
        const deviceInfo = getImprovedDeviceDetection();
        
        console.log('🔍 设备检测结果:', {
            设备类型: deviceInfo.deviceType,
            屏幕尺寸: `${deviceInfo.screenWidth}x${deviceInfo.screenHeight}`,
            UserAgent检测: deviceInfo.isMobileUA ? 'Mobile' : deviceInfo.isTabletUA ? 'Tablet' : 'Desktop',
            最终判断: deviceInfo.isMobile ? 'Mobile' : deviceInfo.isTablet ? 'Tablet' : 'Desktop'
        });
        
        // 检测当前页面类型
        const path = window.location.pathname;
        let pageKey = 'INDEX';
        
        if (path.includes('meetings')) pageKey = 'MEETINGS';
        else if (path.includes('anniversary')) pageKey = 'ANNIVERSARY';
        else if (path.includes('memorial')) pageKey = 'MEMORIAL';
        else if (path.includes('together-days')) pageKey = 'TOGETHER_DAYS';
        
        console.log(`📄 当前页面: ${pageKey}`);
        
        // 查找视频元素
        const videoElements = document.querySelectorAll('video');
        
        if (videoElements.length === 0) {
            console.warn('⚠️ 未找到视频元素');
            return;
        }
        
        videoElements.forEach((video, index) => {
            const correctUrl = generateCorrectCloudinaryUrl(pageKey, deviceInfo);
            
            if (correctUrl && video.src !== correctUrl) {
                console.log(`🔄 修复视频 ${index + 1}:`);
                console.log(`   原URL: ${video.src}`);
                console.log(`   新URL: ${correctUrl}`);
                
                // 更新视频源
                video.src = correctUrl;
                video.load();
                
                // 尝试播放
                video.play().catch(e => {
                    console.log('自动播放被阻止，等待用户交互');
                });
                
                console.log(`✅ 视频 ${index + 1} 已修复`);
            } else if (correctUrl) {
                console.log(`✅ 视频 ${index + 1} URL已正确`);
            }
        });
    }
    
    // 修复VideoManager的设备检测
    function patchVideoManager() {
        if (window.SimpleVideoManager && window.SimpleVideoManager.prototype) {
            const originalGetUserContext = window.SimpleVideoManager.prototype.getUserContext;
            
            window.SimpleVideoManager.prototype.getUserContext = function() {
                const deviceInfo = getImprovedDeviceDetection();
                
                return {
                    isMobile: deviceInfo.isMobile,
                    isTablet: deviceInfo.isTablet,
                    isSlowConnection: false, // 保持原有逻辑
                    userAgent: deviceInfo.userAgent,
                    screenWidth: deviceInfo.screenWidth,
                    screenHeight: deviceInfo.screenHeight,
                    pixelRatio: window.devicePixelRatio || 1
                };
            };
            
            console.log('✅ SimpleVideoManager.getUserContext() 已修复');
        }
        
        // 修复其他CDN管理器
        if (window.HybridCDNManager && window.HybridCDNManager.prototype) {
            const originalAnalyzeUserContext = window.HybridCDNManager.prototype.analyzeUserContext;
            
            window.HybridCDNManager.prototype.analyzeUserContext = function(context = {}) {
                const deviceInfo = getImprovedDeviceDetection();
                
                return {
                    isMobile: deviceInfo.isMobile,
                    isTablet: deviceInfo.isTablet,
                    isSlowConnection: context.isSlowConnection || false,
                    userAgent: deviceInfo.userAgent
                };
            };
            
            console.log('✅ HybridCDNManager.analyzeUserContext() 已修复');
        }
    }
    
    // 提供手动强制设备类型的功能
    window.forceDeviceType = function(type) {
        const validTypes = ['mobile', 'tablet', 'desktop'];
        if (!validTypes.includes(type)) {
            console.error(`❌ 无效的设备类型: ${type}. 有效值: ${validTypes.join(', ')}`);
            return;
        }
        
        // 临时覆盖设备检测
        window._forcedDeviceType = type;
        
        console.log(`🔧 强制设备类型: ${type}`);
        
        // 重新加载视频
        setTimeout(() => {
            fixCurrentPageVideo();
        }, 100);
    };
    
    // 提供诊断功能
    window.diagnoseVideoResolution = function() {
        const deviceInfo = getImprovedDeviceDetection();
        const videoElements = document.querySelectorAll('video');
        
        console.log('🔍 视频分辨率诊断报告:');
        console.log('================================');
        console.log('设备信息:', deviceInfo);
        console.log('视频元素数量:', videoElements.length);
        
        videoElements.forEach((video, index) => {
            console.log(`视频 ${index + 1}:`);
            console.log(`  URL: ${video.src}`);
            console.log(`  尺寸: ${video.videoWidth}x${video.videoHeight}`);
            console.log(`  状态: ${video.readyState >= 2 ? '已加载' : '加载中'}`);
            
            // 分析URL中的参数
            if (video.src.includes('cloudinary.com')) {
                const resMatch = video.src.match(/w_(\d+),h_(\d+)/);
                if (resMatch) {
                    console.log(`  Cloudinary分辨率: ${resMatch[1]}x${resMatch[2]}`);
                }
            }
        });
        
        console.log('================================');
    };
    
    // 主修复流程
    function main() {
        console.log('🚀 开始修复视频分辨率问题...');
        
        // 1. 修复VideoManager
        patchVideoManager();
        
        // 2. 等待页面加载完成后修复当前视频
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', fixCurrentPageVideo);
        } else {
            setTimeout(fixCurrentPageVideo, 1000);
        }
        
        console.log('✅ 修复脚本已应用');
        console.log('💡 可用命令:');
        console.log('  - diagnoseVideoResolution(): 诊断当前视频状态');
        console.log('  - forceDeviceType("desktop"): 强制桌面模式');
        console.log('  - forceDeviceType("mobile"): 强制移动模式');
        console.log('  - forceDeviceType("tablet"): 强制平板模式');
    }
    
    // 启动修复
    main();
    
})();
