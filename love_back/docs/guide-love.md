# Love Website 使用指南

## 🌐 网站访问指南

### ⚠️ 重要提醒：请使用域名访问

**Love Website 必须通过 `love.yuh.cool` 域名访问，不要使用端口号！**

- ✅ **正确访问方式**: https://love.yuh.cool/
- ❌ **错误访问方式**: http://localhost:1314/

**所有的测试页面都需要放在test/目录中**

### 主要页面访问地址

Love Website 通过 `love.yuh.cool` 域名提供服务，所有页面都可以通过简洁的URL直接访问：

#### 🏠 主要页面
- **首页**: https://love.yuh.cool/
- **在一起的日子**: https://love.yuh.cool/together-days
- **纪念日**: https://love.yuh.cool/anniversary
- **相遇回忆**: https://love.yuh.cool/meetings
- **纪念相册**: https://love.yuh.cool/memorial

#### 🧪 测试页面
- **视频加载测试**: https://love.yuh.cool/test/test-video-loading.html
- **视频性能测试**: https://love.yuh.cool/test/test-video-optimization.html
- **API测试**: https://love.yuh.cool/test/test-api.html
- **测试套件**: https://love.yuh.cool/test/test-suite.html

#### 📊 API接口
- **健康检查**: https://love.yuh.cool/api/health
- **留言接口**: https://love.yuh.cool/api/messages
- **在一起天数**: https://love.yuh.cool/api/together-days

## 🎯 页面功能说明

### 🏠 首页 (/)
- **功能**: 网站入口，展示爱情主题和导航
- **特色**: 花朵视频背景，浪漫星空效果
- **访问**: https://love.yuh.cool/

### 💕 在一起的日子 (/together-days)
- **功能**: 计算和展示恋爱天数
- **特色**: 海底视频背景，动态天数计算
- **访问**: https://love.yuh.cool/together-days

### 🎉 纪念日 (/anniversary)
- **功能**: 展示重要纪念日和里程碑
- **特色**: 绿荫视频背景，时间轴展示
- **访问**: https://love.yuh.cool/anniversary

### ✨ 相遇回忆 (/meetings)
- **功能**: 记录相遇的美好时光
- **特色**: 星河视频背景，回忆录展示
- **访问**: https://love.yuh.cool/meetings

### 📸 纪念相册 (/memorial)
- **功能**: 珍贵照片和回忆展示
- **特色**: 海洋视频背景，相册浏览
- **访问**: https://love.yuh.cool/memorial

## 📁 项目目录结构

```
love/
├── 📄 README.md                    # 项目说明文档
├── 📄 PROJECT-STRUCTURE.md         # 项目结构说明 (本文件)
├── 📄 package.json                 # Node.js 依赖配置
├── 📄 server.js                    # 主服务器文件
├── 📄 config.js                    # 全局配置文件
├── 📄 simple-video-manager.js      # 简化视频管理器 ⭐ 新增
├── 📄 enhanced-video-manager.js    # 增强视频管理器 (已弃用)
├── 📄 script.js                    # 主要前端脚本
├── 📄 style.css                    # 主要样式文件
├── 📄 pages.css                    # 页面样式文件
├── 📄 dynamic-styles.js            # 动态样式脚本
├── 📄 romantic-quotes.js           # 浪漫语录脚本
├── 📄 modern-quotes-data.js        # 现代语录数据
├── 📄 performance-report.js        # 性能报告脚本
│
├── 📁 html/                        # HTML页面文件
│   ├── 📄 index.html               # 主页
│   ├── 📄 anniversary.html         # 纪念日页面
│   ├── 📄 meetings.html            # 相遇页面
│   ├── 📄 memorial.html            # 纪念页面
│   ├── 📄 together-days.html       # 在一起的日子页面
│   └── 📄 modern-quotes-data.js    # 现代语录数据
│
├── 📁 test/                        # 测试文件目录
│   ├── 📄 test-*                   # 所有的测试文件都应在此文件夹中
│
├── 📁 scripts/                     # 脚本文件目录
│   ├── 📄 video-optimizer-2k.sh    # 2K视频优化脚本 ⭐
│   ├── 📄 deploy-video-optimization.sh # 视频优化部署脚本 ⭐
│   ├── 📄 import-modern-quotes.js  # 导入现代语录脚本
│   ├── 📄 parse-poetry.js          # 诗词解析脚本
│   ├── 📄 video-manager.js         # 原始视频管理器
│   ├── 📄 video-init.js            # 视频初始化脚本
│   ├── 📄 video-performance-optimizer.js # 视频性能优化器
│   └── 📄 video-test.js            # 视频测试脚本
│
├── 📁 docs/                        # 文档目录
│   ├── 📄 视频加载优化完整方案.md    # 视频优化完整方案 ⭐
│   ├── 📄 总架构方案.md             # 总体架构方案
│   ├── 📄 guide-love.md            # 使用指南
│   ├── 📄 video-optimization-summary.md # 视频优化总结
│   └── 📄 test-execution-report.md # 测试执行报告
│
├── 📁 config/                      # 配置文件目录
│   ├── 📄 CONFIG-README.md         # 配置说明
│   ├── 📄 README.md                # 配置目录说明
│   ├── 📄 server-config.js         # 服务器配置
│   ├── 📄 update-config.js         # 配置更新脚本
│   ├── 📄 config-manager.sh        # 配置管理脚本
│   ├── 📄 manage-config.sh         # 配置管理脚本
│   └── 📄 config-usage-examples.md # 配置使用示例
│
├── 📁 background/                  # 视频背景文件目录 ⭐ 已重构
│
├── 📁 fonts/                       # 字体文件目录
│
├── 📁 data/                        # 数据文件目录
│   ├── 📄 love_messages.db         # 爱情留言数据库
│   ├── 📄 现代美好情话千句搜索_.txt  # 现代情话数据
│   └── 📁 backups/                 # 数据备份目录
│
├── 📁 logs/                        # 日志文件目录
│   ├── 📄 backend.log              # 后端日志
│   └── 📄 manage.log               # 管理日志
│
├── 📁 temp/                        # 临时文件目录
│   ├── 📁 temp_compression/        # 压缩临时文件
│   └── 📁 temp_optimization/       # 优化临时文件
│
└── 📁 node_modules/                # Node.js 依赖包
```

## 🎯 重要文件说明

### ⭐ 视频相关文件 (已简化)
- **`test/test-video-loading.html`** - 视频加载测试页面 ⭐ 新增
- **`test/test-video-optimization.html`** - 视频性能测试页面
- **`simple-video-manager.js`** - 简化的视频管理器 ⭐ 新增
- **`scripts/video-optimizer-2k.sh`** - 保持2K质量的视频优化脚本
- **`docs/视频加载优化完整方案.md`** - 完整的优化方案文档

### � 视频背景系统
每个页面都配备了精心挑选的2K高清视频背景，目录和文件名已完全标准化为英文页面名称：

| 页面 | 视频主题 | 文件位置 | 状态 |
|------|---------|---------|----------|
| 首页 | 花朵绽放 | `background/home/<USER>
| 在一起的日子 | 海底世界 | `background/together-days/together-days.mp4` | ✅ 已标准化 |
| 纪念日 | 绿荫森林 | `background/anniversary/anniversary.mp4` | ✅ 已标准化 |
| 相遇回忆 | 星河璀璨 | `background/meetings/meetings.mp4` | ✅ 已标准化 |
| 纪念相册 | 蔚蓝海洋 | `background/memorial/memorial.mp4` | ✅ 已标准化 |

### � 响应式设计
- **桌面端**: 完整功能体验，高清视频背景
- **移动端**: 自适应布局，优化加载策略
- **平板端**: 中等屏幕优化显示

### 🔧 技术特性
- **智能视频加载**: 根据网络状况自动选择最佳质量
- **渐进式增强**: 视频加载失败时优雅降级到渐变背景
- **性能优化**: H.265/H.264编码，文件大小减少50%+
- **缓存策略**: 智能预加载和缓存管理

## 🚀 快速访问指南

### 🌐 直接访问页面
⚠️ **重要：必须使用域名访问，不要使用端口号！**

```bash
# 主要页面
https://love.yuh.cool/                    # 首页
https://love.yuh.cool/together-days       # 在一起的日子
https://love.yuh.cool/anniversary         # 纪念日
https://love.yuh.cool/meetings            # 相遇回忆
https://love.yuh.cool/memorial            # 纪念相册

# 测试页面
https://love.yuh.cool/test/test-video-loading.html       # 视频加载测试 ⭐ 新增
https://love.yuh.cool/test/test-video-optimization.html  # 视频性能测试
https://love.yuh.cool/test/test-api.html                 # API测试
https://love.yuh.cool/test/test-suite.html               # 测试套件
```

### 📱 移动端访问
所有页面都完美支持移动设备访问，自动适配屏幕大小。

### 🔗 页面间导航
- 每个页面都有导航菜单，可以快速切换
- 支持浏览器前进/后退按钮
- URL直接分享给他人访问

## 🎨 用户体验特色

### ✨ 视觉效果
- **2K高清视频背景**: 每个页面都有独特的主题视频
- **浪漫星空动画**: 首页特有的动态星空效果
- **优雅过渡动画**: 页面切换和元素加载动画
- **响应式布局**: 适配各种设备屏幕

### 🚀 性能优化
- **智能加载**: 根据网络速度自动选择视频质量
- **预加载策略**: 提前加载可能访问的页面
- **缓存机制**: 减少重复加载时间
- **优雅降级**: 网络较差时自动使用备用背景

### 💝 互动功能
- **留言板**: 在首页可以留下爱的留言
- **天数计算**: 自动计算在一起的天数
- **相册浏览**: 纪念相册支持图片浏览
- **响应式交互**: 支持触摸和鼠标操作

## 🔧 开发者指南

### 本地开发环境
```bash
# 克隆项目
git clone [repository-url]
cd love

# 安装依赖
npm install

# 启动开发服务器
npm start
# 或
node server.js

# 访问本地版本
http://localhost:1314
```

### 视频优化
```bash
# 运行视频优化脚本
chmod +x scripts/video-optimizer-2k.sh
./scripts/video-optimizer-2k.sh

# 运行域名访问测试
chmod +x test/test-domain-access.sh
./test/test-domain-access.sh

# 测试视频加载效果
https://love.yuh.cool/test/test-video-loading.html

# 测试优化效果
https://love.yuh.cool/test/test-video-optimization.html
```

### 配置文件
- **`config.js`**: 全局配置，包含所有页面路由和视频配置
- **`server.js`**: Express服务器配置
- **`package.json`**: 项目依赖和脚本

## 📞 技术支持

### ⚠️ 访问方式重要提醒

**必须使用域名访问，不要使用端口号！**

- ✅ **正确**: https://love.yuh.cool/test/test-video-loading.html
- ❌ **错误**: http://localhost:1314/test/test-video-loading.html

**原因说明：**
- 网站配置了域名解析和SSL证书
- 使用端口访问可能导致视频加载失败
- 某些功能只在域名环境下正常工作

### 常见问题
**Q: 视频加载很慢怎么办？**
A: 访问 https://love.yuh.cool/test/test-video-loading.html 检查视频加载状态

**Q: 移动端显示异常？**
A: 检查网络连接，清除浏览器缓存后重试

**Q: 如何添加新页面？**
A: 在 `html/` 目录添加HTML文件，在 `config.js` 中配置路由

**Q: 测试页面无法访问？**
A: 确保使用 https://love.yuh.cool/test/ 开头的完整域名URL

### 相关文档
- **完整优化方案**: `docs/视频加载优化完整方案.md`
- **架构说明**: `docs/总架构方案.md`
- **项目结构**: `PROJECT-STRUCTURE.md`

## 🎉 最新更新 (2025-07-29)

### ✅ 视频文件完全标准化 (最新)
- **重命名了所有视频文件**：文件名与页面名称完全对应
  - `home/flower-background.mp4` → `home/home.mp4`
  - `meetings/揽一池星河入梦，画一弦月色温柔。.mp4` → `meetings/meetings.mp4`
  - `anniversary/Mount Rainier National Park Washington State 7.mp4` → `anniversary/anniversary.mp4`
  - `together-days/海底.mp4` → `together-days/together-days.mp4`
  - `memorial/View Of The Sea With Blue Waters.mp4` → `memorial/memorial.mp4`
- **简化了配置逻辑**：删除了复杂的URL编码处理
- **更新了所有相关文件**：config.js、HTML页面、测试脚本
- **验证了所有路径**：域名访问测试14/14通过 ✅

### ✅ 视频目录结构重构
- **重命名了所有视频目录**：从中文名改为英文页面名称
  - `花` → `home` (首页花朵背景)
  - `星河` → `meetings` (相遇回忆)
  - `绿荫` → `anniversary` (纪念日)
  - `海底` → `together-days` (在一起的日子)
  - `sea` → `memorial` (纪念相册)
- **更新了配置文件**：`config.js` 中的所有视频路径

### ✅ 视频加载问题已解决
- **删除了复杂的视频优化策略**，改为直接加载原始视频文件
- **创建了简化的视频管理器** (`simple-video-manager.js`)
- **更新了所有HTML页面**，使用新的简单视频管理器
- **新增了视频加载测试页面**: https://love.yuh.cool/test/test-video-loading.html

### 🔧 技术改进
- **简化了视频加载逻辑**：删除了网络检测、设备性能检测等复杂功能
- **统一了错误处理**：失败时直接显示渐变背景
- **提升了可靠性**：减少了加载失败的可能性

### 📋 测试清单
请使用以下链接测试网站功能（**必须使用域名访问**）：

- ✅ **主页**: https://love.yuh.cool/
- ✅ **视频加载测试**: https://love.yuh.cool/test/test-video-loading.html
- ✅ **纪念日页面**: https://love.yuh.cool/anniversary
- ✅ **相遇回忆页面**: https://love.yuh.cool/meetings
- ✅ **纪念相册页面**: https://love.yuh.cool/memorial
- ✅ **在一起的日子页面**: https://love.yuh.cool/together-days

---

**� Love Website - 记录我们的美好时光**
**🌐 访问地址**: https://love.yuh.cool/
**🧪 测试地址**: https://love.yuh.cool/test/test-video-loading.html
