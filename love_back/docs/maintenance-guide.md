# Love Website 维护指南

## 概述
本文档提供Love Website视频系统的日常维护、故障排除和性能优化指南。

## 系统架构概览

### 核心组件
1. **设备检测工具** (`device-detection-utils.js`)
   - 统一的设备检测逻辑
   - 支持移动、平板、桌面设备识别
   - 提供详细的设备信息日志

2. **CDN管理器** (`hybrid-cdn-manager.js`, `cloudinary-load-balancer.js`)
   - 智能CDN选择和负载均衡
   - 双重URL故障转移机制
   - 自动设备适配

3. **视频管理器** (`simple-video-manager.js`)
   - 视频加载和播放控制
   - 预加载和缓存管理
   - 错误处理和重试机制

4. **紧急修复系统** (`emergency-video-fix.js`)
   - 页面加载早期生效
   - 全局故障恢复
   - 手动修复命令

5. **生产监控系统** (`production-monitoring.js`)
   - 实时性能监控
   - 错误追踪和告警
   - 自动回滚机制

## 日常维护任务

### 每日检查 (自动化)
- [ ] 视频加载成功率 > 99%
- [ ] 平均加载时间 < 3秒
- [ ] 错误率 < 0.5%
- [ ] Cloudinary API状态正常

### 每周检查 (手动)
- [ ] 检查监控日志异常
- [ ] 验证所有页面视频正常
- [ ] 检查跨浏览器兼容性
- [ ] 更新性能基准数据

### 每月检查 (手动)
- [ ] 分析用户反馈和投诉
- [ ] 检查CDN使用量和成本
- [ ] 更新浏览器兼容性测试
- [ ] 备份配置和脚本文件

## 故障排除指南

### 常见问题诊断

#### 1. 视频无法加载
**症状：** 视频元素显示但不播放，或显示错误图标

**诊断步骤：**
```javascript
// 1. 检查URL生成
console.log('主URL:', generateWorkingVideoUrl('INDEX'));
console.log('备用URL:', generateFallbackVideoUrl('INDEX'));

// 2. 检查设备检测
console.log('设备信息:', DeviceDetectionUtils.detectDevice());

// 3. 检查网络连接
fetch(generateWorkingVideoUrl('INDEX'), {method: 'HEAD'})
  .then(r => console.log('URL状态:', r.status))
  .catch(e => console.error('URL错误:', e));
```

**解决方案：**
- 检查Cloudinary服务状态
- 验证URL格式正确性
- 手动执行 `emergencyVideoFix()`
- 检查浏览器控制台错误

#### 2. 视频分辨率不正确
**症状：** 视频播放但分辨率不是预期的2K或1080p

**诊断步骤：**
```javascript
// 检查视频实际分辨率
document.querySelectorAll('video').forEach((v, i) => {
  console.log(`视频${i+1}: ${v.videoWidth}x${v.videoHeight}, URL: ${v.src}`);
});

// 检查设备检测结果
const deviceInfo = DeviceDetectionUtils.detectDevice();
console.log('设备类型:', deviceInfo.deviceType);
console.log('预期分辨率:', DeviceDetectionUtils.getVideoTransforms(deviceInfo));
```

**解决方案：**
- 验证设备检测逻辑
- 检查URL中的分辨率参数
- 清除浏览器缓存
- 重新加载页面

#### 3. 加载速度慢
**症状：** 视频加载时间超过5秒

**诊断步骤：**
```javascript
// 检查性能指标
if (window.productionMonitor) {
  console.log('监控报告:', window.productionMonitor.getReport());
}

// 测试网络速度
const startTime = performance.now();
fetch(generateWorkingVideoUrl('INDEX'), {method: 'HEAD'})
  .then(() => {
    console.log('网络延迟:', performance.now() - startTime, 'ms');
  });
```

**解决方案：**
- 检查CDN节点状态
- 优化视频压缩参数
- 启用预加载机制
- 检查网络连接质量

#### 4. 跨浏览器兼容性问题
**症状：** 某些浏览器无法正常播放视频

**诊断步骤：**
```javascript
// 检查浏览器支持
console.log('User Agent:', navigator.userAgent);
console.log('视频格式支持:', {
  mp4: document.createElement('video').canPlayType('video/mp4'),
  webm: document.createElement('video').canPlayType('video/webm')
});

// 检查JavaScript功能
console.log('功能检查:', {
  fetch: typeof fetch !== 'undefined',
  Promise: typeof Promise !== 'undefined',
  arrow: (() => true)() === true
});
```

**解决方案：**
- 添加浏览器特定的兼容性代码
- 使用polyfill补充缺失功能
- 提供降级方案
- 更新浏览器支持列表

### 紧急故障处理

#### 立即响应 (5分钟内)
1. **确认问题范围**
   ```bash
   # 检查服务状态
   curl -I https://love.yuh.cool/
   curl -I https://res.cloudinary.com/dcglebc2w/video/upload/love-website/home.mp4
   ```

2. **启动紧急修复**
   ```javascript
   // 在浏览器控制台执行
   emergencyVideoFix();
   fixAllVideos();
   ```

3. **激活监控**
   ```javascript
   // 检查监控状态
   if (window.productionMonitor) {
     console.log(window.productionMonitor.getReport());
   }
   ```

#### 短期修复 (30分钟内)
1. **分析错误日志**
2. **执行回滚程序**
3. **通知相关人员**
4. **更新状态页面**

#### 长期解决 (2小时内)
1. **根因分析**
2. **制定修复方案**
3. **测试修复效果**
4. **部署修复版本**

## 性能优化建议

### 视频加载优化
1. **预加载策略**
   - 首页视频立即加载
   - 其他页面视频预加载
   - 智能缓存管理

2. **CDN优化**
   - 选择最近的CDN节点
   - 启用HTTP/2
   - 配置适当的缓存策略

3. **参数优化**
   - 使用 `q_auto` 自动质量
   - 使用 `f_auto` 自动格式
   - 避免复杂的变换参数

### 监控优化
1. **关键指标**
   - 视频加载成功率
   - 平均加载时间
   - 错误率和类型
   - 用户设备分布

2. **告警设置**
   - 错误率 > 5% 立即告警
   - 加载时间 > 5秒 告警
   - 连续失败 > 5次 告警

## 配置管理

### 重要配置文件
- `config.js` - 主配置文件
- `device-detection-utils.js` - 设备检测配置
- `production-monitoring.js` - 监控配置

### 配置更新流程
1. 在测试环境验证
2. 备份当前配置
3. 逐步部署更新
4. 监控更新效果
5. 必要时回滚

### 环境变量
```javascript
// 生产环境配置
const PRODUCTION_CONFIG = {
  monitoringEnabled: true,
  errorThreshold: 0.05,
  performanceThreshold: 5000,
  reportingInterval: 30000
};
```

## 联系信息和升级路径

### 技术支持联系方式
- **一级支持：** 运维团队
- **二级支持：** 开发团队
- **三级支持：** 架构师团队

### 升级决策流程
1. **问题评估** - 确定问题严重程度
2. **影响分析** - 评估用户影响范围
3. **解决方案** - 制定技术解决方案
4. **风险评估** - 评估修复风险
5. **执行决策** - 决定是否立即修复

### 文档更新
- 本文档应随系统更新而更新
- 所有重大变更都应记录
- 定期审查和优化维护流程

## 备份和恢复

### 备份策略
- **代码备份：** Git版本控制
- **配置备份：** 每次部署前备份
- **监控数据：** 定期导出和存档

### 恢复程序
1. **快速恢复：** 使用紧急修复脚本
2. **完整恢复：** 从备份恢复所有文件
3. **数据恢复：** 恢复监控和配置数据

---

**最后更新：** 2025-01-20  
**版本：** 1.0  
**维护人员：** 开发团队
