#!/bin/bash

# 💕 Love Site 配置管理脚本
# 统一管理Love网站的所有配置，包括域名、端口、路径等

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# 项目路径
CONFIG_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOVE_DIR="$(dirname "$CONFIG_DIR")"
ENV_FILE="$LOVE_DIR/.env"
ENV_EXAMPLE="$CONFIG_DIR/.env.example"
CONFIG_JS="$LOVE_DIR/config.js"
SERVER_CONFIG="$CONFIG_DIR/server-config.js"

# 显示标题
show_title() {
    echo -e "${PURPLE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                    💕 Love Site 配置管理器                    ║"
    echo "║                   Configuration Manager                      ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

# 显示当前配置
show_current_config() {
    echo -e "${CYAN}=== 当前配置信息 ===${NC}"
    
    if [ -f "$ENV_FILE" ]; then
        echo -e "${GREEN}✅ 环境配置文件: .env${NC}"
        echo -e "${BLUE}主要配置项:${NC}"
        
        # 读取并显示主要配置
        if grep -q "LOVE_DOMAIN_PRODUCTION" "$ENV_FILE"; then
            DOMAIN=$(grep "LOVE_DOMAIN_PRODUCTION" "$ENV_FILE" | cut -d'=' -f2)
            echo -e "  🌐 生产域名: ${GREEN}$DOMAIN${NC}"
        fi
        
        if grep -q "LOVE_PORT" "$ENV_FILE"; then
            PORT=$(grep "LOVE_PORT" "$ENV_FILE" | cut -d'=' -f2)
            echo -e "  🔌 服务端口: ${GREEN}$PORT${NC}"
        fi
        
        if grep -q "NODE_ENV" "$ENV_FILE"; then
            ENV=$(grep "NODE_ENV" "$ENV_FILE" | cut -d'=' -f2)
            echo -e "  🏗️  运行环境: ${GREEN}$ENV${NC}"
        fi
    else
        echo -e "${YELLOW}⚠️  环境配置文件不存在，使用默认配置${NC}"
        echo -e "  🌐 默认域名: ${GREEN}love.yuh.cool${NC}"
        echo -e "  🔌 默认端口: ${GREEN}1314${NC}"
        echo -e "  🏗️  默认环境: ${GREEN}production${NC}"
    fi
    
    echo ""
}

# 创建环境配置文件
create_env_file() {
    echo -e "${CYAN}=== 创建环境配置文件 ===${NC}"
    
    if [ -f "$ENV_FILE" ]; then
        echo -e "${YELLOW}⚠️  .env文件已存在${NC}"
        read -p "是否要覆盖现有配置？(y/N): " confirm
        if [[ ! $confirm =~ ^[Yy]$ ]]; then
            echo -e "${BLUE}ℹ️  操作已取消${NC}"
            return
        fi
    fi
    
    # 获取用户输入
    echo -e "${BLUE}请输入配置信息:${NC}"
    
    read -p "生产环境域名 (默认: love.yuh.cool): " domain
    domain=${domain:-love.yuh.cool}
    
    read -p "服务端口 (默认: 1314): " port
    port=${port:-1314}
    
    read -p "运行环境 (production/development, 默认: production): " env
    env=${env:-production}
    
    # 创建.env文件
    cp "$ENV_EXAMPLE" "$ENV_FILE"
    
    # 更新配置值
    sed -i "s/LOVE_DOMAIN_PRODUCTION=.*/LOVE_DOMAIN_PRODUCTION=$domain/" "$ENV_FILE"
    sed -i "s/LOVE_PORT=.*/LOVE_PORT=$port/" "$ENV_FILE"
    sed -i "s/NODE_ENV=.*/NODE_ENV=$env/" "$ENV_FILE"
    
    echo -e "${GREEN}✅ 环境配置文件已创建: .env${NC}"
    echo -e "${BLUE}配置内容:${NC}"
    echo -e "  🌐 域名: $domain"
    echo -e "  🔌 端口: $port"
    echo -e "  🏗️  环境: $env"
}

# 编辑配置文件
edit_config() {
    echo -e "${CYAN}=== 编辑配置文件 ===${NC}"
    
    echo "选择要编辑的配置文件:"
    echo "1) 环境变量配置 (.env)"
    echo "2) 前端配置 (config.js)"
    echo "3) 服务器配置 (server-config.js)"
    echo "4) 返回主菜单"
    
    read -p "请选择 (1-4): " choice
    
    case $choice in
        1)
            if [ ! -f "$ENV_FILE" ]; then
                echo -e "${YELLOW}⚠️  .env文件不存在，是否创建？(y/N): ${NC}"
                read -p "" create_confirm
                if [[ $create_confirm =~ ^[Yy]$ ]]; then
                    create_env_file
                    return
                else
                    echo -e "${BLUE}ℹ️  操作已取消${NC}"
                    return
                fi
            fi
            
            echo -e "${BLUE}📝 打开环境配置文件...${NC}"
            ${EDITOR:-nano} "$ENV_FILE"
            ;;
        2)
            echo -e "${BLUE}📝 打开前端配置文件...${NC}"
            ${EDITOR:-nano} "$CONFIG_JS"
            ;;
        3)
            echo -e "${BLUE}📝 打开服务器配置文件...${NC}"
            ${EDITOR:-nano} "$SERVER_CONFIG"
            ;;
        4)
            return
            ;;
        *)
            echo -e "${RED}❌ 无效选择${NC}"
            ;;
    esac
}

# 验证配置
validate_config() {
    echo -e "${CYAN}=== 验证配置 ===${NC}"
    
    local errors=0
    
    # 检查环境配置文件
    if [ -f "$ENV_FILE" ]; then
        echo -e "${GREEN}✅ 环境配置文件存在${NC}"
        
        # 检查必要的配置项
        required_vars=("LOVE_DOMAIN_PRODUCTION" "LOVE_PORT" "NODE_ENV")
        for var in "${required_vars[@]}"; do
            if grep -q "^$var=" "$ENV_FILE"; then
                value=$(grep "^$var=" "$ENV_FILE" | cut -d'=' -f2)
                if [ -n "$value" ]; then
                    echo -e "  ✅ $var: $value"
                else
                    echo -e "  ${RED}❌ $var: 值为空${NC}"
                    ((errors++))
                fi
            else
                echo -e "  ${RED}❌ $var: 配置项缺失${NC}"
                ((errors++))
            fi
        done
    else
        echo -e "${RED}❌ 环境配置文件不存在${NC}"
        ((errors++))
    fi
    
    # 检查前端配置文件
    if [ -f "$CONFIG_JS" ]; then
        echo -e "${GREEN}✅ 前端配置文件存在${NC}"
        
        # 检查配置文件语法
        if node -c "$CONFIG_JS" 2>/dev/null; then
            echo -e "  ✅ 语法检查通过"
        else
            echo -e "  ${RED}❌ 语法检查失败${NC}"
            ((errors++))
        fi
    else
        echo -e "${RED}❌ 前端配置文件不存在${NC}"
        ((errors++))
    fi
    
    # 检查服务器配置文件
    if [ -f "$SERVER_CONFIG" ]; then
        echo -e "${GREEN}✅ 服务器配置文件存在${NC}"
        
        # 检查配置文件语法
        if node -c "$SERVER_CONFIG" 2>/dev/null; then
            echo -e "  ✅ 语法检查通过"
        else
            echo -e "  ${RED}❌ 语法检查失败${NC}"
            ((errors++))
        fi
    else
        echo -e "${RED}❌ 服务器配置文件不存在${NC}"
        ((errors++))
    fi
    
    echo ""
    if [ $errors -eq 0 ]; then
        echo -e "${GREEN}🎉 所有配置验证通过！${NC}"
    else
        echo -e "${RED}❌ 发现 $errors 个配置问题，请修复后重试${NC}"
    fi
}

# 备份配置
backup_config() {
    echo -e "${CYAN}=== 备份配置文件 ===${NC}"
    
    local backup_dir="$LOVE_DIR/config-backups"
    local timestamp=$(date +"%Y%m%d_%H%M%S")
    local backup_name="config_backup_$timestamp"
    
    mkdir -p "$backup_dir/$backup_name"
    
    # 备份配置文件
    local files_backed_up=0
    
    if [ -f "$ENV_FILE" ]; then
        cp "$ENV_FILE" "$backup_dir/$backup_name/"
        echo -e "${GREEN}✅ 已备份: .env${NC}"
        ((files_backed_up++))
    fi
    
    if [ -f "$CONFIG_JS" ]; then
        cp "$CONFIG_JS" "$backup_dir/$backup_name/"
        echo -e "${GREEN}✅ 已备份: config.js${NC}"
        ((files_backed_up++))
    fi
    
    if [ -f "$SERVER_CONFIG" ]; then
        cp "$SERVER_CONFIG" "$backup_dir/$backup_name/"
        echo -e "${GREEN}✅ 已备份: server-config.js${NC}"
        ((files_backed_up++))
    fi
    
    if [ $files_backed_up -gt 0 ]; then
        echo -e "${GREEN}🎉 配置备份完成！${NC}"
        echo -e "${BLUE}备份位置: $backup_dir/$backup_name${NC}"
    else
        echo -e "${YELLOW}⚠️  没有找到可备份的配置文件${NC}"
        rmdir "$backup_dir/$backup_name" 2>/dev/null || true
    fi
}

# 主菜单
main_menu() {
    while true; do
        show_title
        show_current_config
        
        echo -e "${WHITE}请选择操作:${NC}"
        echo "1) 📝 创建环境配置文件"
        echo "2) ✏️  编辑配置文件"
        echo "3) ✅ 验证配置"
        echo "4) 💾 备份配置"
        echo "5) 🚪 退出"
        echo ""
        
        read -p "请输入选择 (1-5): " choice
        
        case $choice in
            1)
                create_env_file
                ;;
            2)
                edit_config
                ;;
            3)
                validate_config
                ;;
            4)
                backup_config
                ;;
            5)
                echo -e "${GREEN}👋 再见！${NC}"
                exit 0
                ;;
            *)
                echo -e "${RED}❌ 无效选择，请重试${NC}"
                ;;
        esac
        
        echo ""
        read -p "按回车键继续..."
        clear
    done
}

# 检查依赖
check_dependencies() {
    if ! command -v node &> /dev/null; then
        echo -e "${RED}❌ Node.js 未安装，请先安装 Node.js${NC}"
        exit 1
    fi
}

# 主程序
main() {
    check_dependencies
    clear
    main_menu
}

# 如果直接运行此脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
