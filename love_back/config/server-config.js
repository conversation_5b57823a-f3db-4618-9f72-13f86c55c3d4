/**
 * Love Website Server Configuration
 * 服务器端统一配置文件 - 管理所有服务器相关配置
 * 
 * 使用方法：
 * const config = require('./server-config.js');
 * console.log(config.DOMAIN.PRODUCTION);
 */

const path = require('path');

// 环境检测
const isDevelopment = process.env.NODE_ENV === 'development' || 
                     process.env.NODE_ENV !== 'production';

const config = {
    // 域名配置
    DOMAIN: {
        // 生产环境域名
        PRODUCTION: 'love.yuh.cool',
        
        // 开发环境域名
        DEVELOPMENT: 'localhost',
        
        // 获取当前环境的域名
        get current() {
            return isDevelopment ? this.DEVELOPMENT : this.PRODUCTION;
        },
        
        // 获取完整的基础URL
        get baseUrl() {
            const protocol = isDevelopment ? 'http' : 'https';
            const port = isDevelopment ? `:${config.SERVER.PORT}` : '';
            return `${protocol}://${this.current}${port}`;
        }
    },

    // 服务器配置
    SERVER: {
        // 内部端口
        PORT: process.env.LOVE_PORT || 1314,
        
        // 外部端口
        EXTERNAL_PORT: {
            HTTP: 80,
            HTTPS: 443
        },
        
        // 主机配置
        HOST: process.env.LOVE_HOST || '0.0.0.0',
        
        // 数据库配置
        DATABASE: {
            PATH: process.env.LOVE_DB_PATH || path.join(__dirname, '..', 'data', 'love_messages.db'),
            BACKUP_DIR: process.env.LOVE_BACKUP_DIR || path.join(__dirname, '..', 'data', 'backups')
        },
        
        // 日志配置
        LOGS: {
            DIR: process.env.LOVE_LOG_DIR || path.join(__dirname, '..', 'logs'),
            FILE: process.env.LOVE_LOG_FILE || 'backend.log',
            get fullPath() {
                return path.join(this.DIR, this.FILE);
            }
        },

        // 静态文件配置
        STATIC: {
            HTML_DIR: path.join(__dirname, '..', 'html'),
            FONTS_DIR: path.join(__dirname, '..', 'fonts'),
            BACKGROUND_DIR: path.join(__dirname, '..', 'background'),
            TEST_DIR: path.join(__dirname, '..', 'test')
        }
    },

    // API配置
    API: {
        BASE_PATH: '/api',
        VERSION: 'v1',
        
        // API端点
        ENDPOINTS: {
            MESSAGES: '/messages',
            HEALTH: '/health',
            TOGETHER_DAYS: '/together-days',
            TIMELINE: '/timeline',
            MEMORIES: '/memories',
            MODERN_QUOTES: '/modern-quotes'
        },
        
        // 分页配置
        PAGINATION: {
            DEFAULT_PAGE_SIZE: 20,
            MAX_PAGE_SIZE: 100
        },
        
        // 限流配置
        RATE_LIMIT: {
            WINDOW_MS: 15 * 60 * 1000, // 15分钟
            MAX_REQUESTS: 100 // 每个IP最多100个请求
        }
    },

    // 环境配置
    ENV: {
        NODE_ENV: process.env.NODE_ENV || 'production',
        isDevelopment,
        isProduction: !isDevelopment,
        
        // 获取环境名称
        get name() {
            return this.isDevelopment ? 'development' : 'production';
        }
    },

    // 安全配置
    SECURITY: {
        // CORS配置
        CORS: {
            origin: function(origin, callback) {
                // 在开发环境允许所有来源
                if (isDevelopment) {
                    return callback(null, true);
                }
                
                // 生产环境只允许指定域名
                const allowedOrigins = [
                    `https://${config.DOMAIN.PRODUCTION}`,
                    `http://${config.DOMAIN.PRODUCTION}`,
                ];
                
                if (!origin || allowedOrigins.includes(origin)) {
                    callback(null, true);
                } else {
                    callback(new Error('Not allowed by CORS'));
                }
            },
            credentials: true
        },
        
        // 请求体大小限制
        BODY_LIMIT: '10mb',
        
        // 文件上传限制
        UPLOAD_LIMIT: '50mb'
    },

    // 数据库配置
    DATABASE: {
        // SQLite配置
        SQLITE: {
            // 连接选项
            OPTIONS: {
                verbose: isDevelopment ? console.log : null
            },
            
            // 备份配置
            BACKUP: {
                INTERVAL: 24 * 60 * 60 * 1000, // 24小时
                MAX_BACKUPS: 30, // 保留30个备份
                COMPRESSION: true
            }
        }
    },

    // 工具函数
    UTILS: {
        // 获取完整的API URL
        getApiUrl: function(endpoint = '') {
            return `${config.API.BASE_PATH}${endpoint}`;
        },
        
        // 获取完整的页面URL
        getPageUrl: function(path = '') {
            const cleanPath = path.startsWith('/') ? path : `/${path}`;
            return `${config.DOMAIN.baseUrl}${cleanPath}`;
        },
        
        // 检查是否为有效的API端点
        isValidEndpoint: function(endpoint) {
            return Object.values(config.API.ENDPOINTS).includes(endpoint);
        },
        
        // 获取数据库完整路径
        getDatabasePath: function() {
            return config.SERVER.DATABASE.PATH;
        },
        
        // 获取日志完整路径
        getLogPath: function() {
            return config.SERVER.LOGS.fullPath;
        }
    }
};

module.exports = config;
