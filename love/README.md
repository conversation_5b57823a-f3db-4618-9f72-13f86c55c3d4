# 💕 Love Site - 情侣专属网站

一个为情侣打造的温馨网站，包含留言板、纪念日、相遇记录等功能。

## 🎯 项目概述

Love Site是一个完整的情侣网站解决方案，具有现代化的架构设计和企业级的部署方案。项目采用模块化设计，便于维护和扩展。

### 🌟 核心特性
- 💌 **留言板系统** - 支持实时留言、编辑、删除功能
- 📅 **纪念日管理** - 记录重要日期和里程碑
- 💑 **相遇记录** - 珍藏美好回忆的时光轴
- 🎨 **响应式设计** - 完美适配桌面和移动设备
- 🔒 **数据安全** - 完善的备份恢复机制
- ⚡ **高性能** - systemd服务管理，自动重启
- 🛠️ **易管理** - 统一管理脚本，一键操作

## 🏗️ 系统架构

### 技术栈
- **后端**: Node.js + Express + SQLite3
- **前端**: HTML5 + CSS3 + JavaScript (原生)
- **服务器**: Ubuntu + Nginx + systemd
- **SSL**: Let's Encrypt自动续期
- **端口**: 1314 (内部) + 443 (HTTPS)

### 服务架构
```
Internet → Nginx (443) → Love Backend (1314) → SQLite Database
                ↓
            Static Files (html/, test/)
```

## 📁 项目结构

```
love/                           # 项目根目录
├── 🗂️ data/                    # 数据目录
│   ├── love_messages.db        # 主数据库
│   └── backups/               # 数据库备份
├── 🎬 background/              # 背景视频资源
│   ├── home/                  # 首页背景视频 (home.mp4)
│   ├── together-days/         # 在一起的日子背景视频 (together-days.mp4)
│   ├── anniversary/           # 纪念日背景视频 (anniversary.mp4)
│   ├── meetings/              # 相遇记录背景视频 (meetings.mp4)
│   └── memorial/              # 纪念页面背景视频 (memorial.mp4)
├── 🌐 html/                    # 前端页面
│   ├── index.html             # 主页
│   ├── anniversary.html       # 纪念日页面
│   ├── meetings.html          # 相遇记录
│   ├── memorial.html          # 纪念页面
│   └── together-days.html     # 在一起的日子
├── 🧪 test/                    # 测试文件
│   └── test-api.html          # API测试页面
├── ⚙️ config/                  # 配置文件
│   └── nginx-love-api.conf    # Nginx完整配置
├── 📝 logs/                    # 日志文件
│   └── backend.log            # 后端日志
├── 📜 scripts/                 # 脚本目录 (预留)
├── 🛠️ manage.sh                # 统一管理脚本 ⭐
├── 🖥️ server.js                # 后端服务
├── 🎨 style.css                # 主样式
├── 🎨 pages.css                # 页面样式
├── 📜 script.js                # 前端脚本
├── 💕 romantic-quotes.js       # 浪漫话语
├── 📦 package.json             # 项目配置
├── 📖 README.md                # 项目文档 (本文件)
└── 📋 USAGE.md                 # 使用指南
```

## 🚀 快速开始

### 一键启动
```bash
./manage.sh start
```

### 查看状态
```bash
./manage.sh status
```

### 完整管理界面
```bash
./manage.sh
```

## 🌐 网络配置架构

### 独立域名部署 - 宝塔面板
Love项目使用独立域名通过宝塔面板进行部署和管理：

```
https://love.yuh.cool/
├── /                    → Love网站主页 ✅
├── /together-days       → 在一起的日子页面
├── /anniversary         → 纪念日页面
├── /meetings            → 相遇记录页面
├── /memorial            → 纪念页面
├── /api/                → Love API服务 (端口1314)
├── /verify              → 测试验证页面 (用于所有测试)
├── /style.css           → Love样式文件
├── /script.js           → Love脚本文件
└── /background/         → Love背景资源
    ├── /home/<USER>
    ├── /together-days/  → 在一起的日子背景视频 (together-days.mp4)
    ├── /anniversary/    → 纪念日背景视频 (anniversary.mp4)
    ├── /meetings/       → 相遇记录背景视频 (meetings.mp4)
    └── /memorial/       → 纪念页面背景视频 (memorial.mp4)
```

### 宝塔面板配置管理
- **域名绑定**: love.yuh.cool → 完整指向 localhost:1314
- **SSL证书**: 通过宝塔面板自动申请和管理Let's Encrypt证书
- **反向代理**: 宝塔面板配置反向代理，将域名请求转发到内部端口1314
- **静态文件**: 直接通过Nginx服务静态资源（CSS、JS、背景视频等）

**注意**: 项目通过宝塔面板部署，所有域名和SSL配置通过宝塔面板管理。

## 🔧 管理功能

### 核心命令
```bash
# 快速操作
./manage.sh start      # 启动所有服务
./manage.sh stop       # 停止所有服务
./manage.sh restart    # 重启所有服务
./manage.sh status     # 查看系统状态
./manage.sh logs       # 查看日志

# 交互式管理
./manage.sh            # 进入管理界面
```

### 🎛️ 管理界面功能
1. **📊 状态监控** - 实时查看服务、数据库、SSL状态
2. **⚙️ 服务管理** - systemd服务控制、开机自启管理
3. **💾 数据库管理** - 备份、恢复、统计、维护
4. **🚀 部署管理** - 生产环境部署、宝塔面板配置管理
5. **🔍 系统验证** - 验证服务状态、测试外部访问（使用 /verify 路径）
6. **📊 监控管理** - 性能监控、错误分析
7. **🔧 故障排除** - 自动诊断和修复
8. **📁 项目管理** - 文件清理、结构优化

### 🌐 宝塔面板集成功能
- **域名管理**: 通过宝塔面板绑定和管理 love.yuh.cool 域名
- **SSL证书**: 宝塔面板自动申请、续期和管理Let's Encrypt证书
- **反向代理**: 宝塔配置反向代理，将域名请求转发到localhost:1314
- **访问日志**: 通过宝塔面板查看网站访问日志和统计数据
- **性能监控**: 利用宝塔面板的监控功能跟踪网站性能

### 🔧 测试和验证
- **测试路径**: 所有测试使用 https://love.yuh.cool/verify 进行访问和验证
- **API测试**: 通过 /verify 路径访问API测试页面
- **功能验证**: 验证所有页面和功能的正常运行
- **性能测试**: 监控网站响应时间和资源加载

## 🌐 访问地址

- **🌐 网站首页**: https://love.yuh.cool/
- **📅 在一起的日子**: https://love.yuh.cool/together-days
- **🎉 纪念日**: https://love.yuh.cool/anniversary
- **💑 相遇记录**: https://love.yuh.cool/meetings
- **🎁 纪念页面**: https://love.yuh.cool/memorial
- **🔗 API接口**: https://love.yuh.cool/api/
- **🔧 测试验证**: https://love.yuh.cool/verify

## 🔌 API接口

### 留言系统
```http
GET    /api/messages              # 获取所有留言
POST   /api/messages              # 创建新留言
PUT    /api/messages/:id          # 更新留言
DELETE /api/messages/:id          # 删除留言
GET    /api/messages/paginated    # 分页获取留言
GET    /api/messages/stats        # 留言统计
```

### 系统接口
```http
GET    /api/health                # 健康检查
```

### 请求示例
```bash
# 创建留言
curl -X POST https://love.yuh.cool/api/messages \
  -H "Content-Type: application/json" \
  -d '{"author":"Yu","content":"我爱你 💕"}'

# 获取留言
curl https://love.yuh.cool/api/messages

# 健康检查
curl https://love.yuh.cool/api/health

# 测试页面访问
curl https://love.yuh.cool/verify
```

## 💾 数据库设计

### 主表结构 (love_messages_new)
```sql
CREATE TABLE love_messages_new (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    author TEXT NOT NULL CHECK(author IN ('Yu', 'Wang', 'Other')),
    content TEXT NOT NULL,
    created_timestamp INTEGER NOT NULL,
    updated_timestamp INTEGER NOT NULL,
    beijing_date TEXT NOT NULL,           -- 北京时间日期
    beijing_datetime TEXT NOT NULL,       -- 北京时间完整时间
    client_ip TEXT DEFAULT '',
    status TEXT DEFAULT 'active' CHECK(status IN ('active', 'deleted')),
    version INTEGER DEFAULT 1
);
```

### 数据管理
- **自动备份**: 支持定时和手动备份
- **完整性检查**: 定期验证数据库完整性
- **性能优化**: 自动VACUUM和索引重建
- **版本控制**: 数据结构版本管理

## 🛡️ 安全特性

### 服务安全
- systemd服务隔离
- 非特权用户运行
- 自动重启机制
- 资源限制保护

### 网络安全
- HTTPS强制重定向
- 安全头部配置
- CORS策略控制
- 反向代理保护

### 数据安全
- 定期自动备份
- 操作前安全备份
- 数据完整性验证
- 恢复点管理

## 🚀 部署指南

### 系统要求
- Ubuntu 18.04+ / CentOS 7+
- Node.js 16+
- Nginx 1.18+
- SQLite3
- SSL证书 (Let's Encrypt)

### 完整部署
```bash
# 1. 克隆项目
git clone <repository> /root/workspace/love
cd /root/workspace/love

# 2. 安装依赖
npm install

# 3. 一键部署
./manage.sh start

# 4. 配置Nginx (如需要)
./manage.sh
# 选择: 部署管理 → 配置Nginx
```

### 配置文件
项目包含完整的Nginx配置文件 (`config/nginx-love-api.conf`)，包括：
- SSL配置
- 反向代理
- 静态文件服务
- 安全头部
- CORS支持

## 🔧 故障排除

### 常见问题

#### 1. 留言保存失败
**症状**: "保存留言失败：留言不存在"
**解决**: 
```bash
./manage.sh restart
# 或使用故障排除功能
./manage.sh → 故障排除 → 修复后端服务
```

#### 2. 服务无法启动
**症状**: 端口占用或服务启动失败
**解决**:
```bash
./manage.sh → 故障排除 → 修复后端服务
# 或检查端口占用
ss -tuln | grep 1314
```

#### 3. 网站无法访问
**症状**: 域名无法访问
**解决**:
```bash
./manage.sh status  # 检查服务状态
./manage.sh → 故障排除 → 修复Nginx配置
```

### 日志分析
```bash
# 查看应用日志
./manage.sh logs

# 查看系统服务日志
sudo journalctl -u love-site -f

# 查看Nginx日志
sudo tail -f /var/log/nginx/error.log
```

## 📊 监控和维护

### 定期维护任务
- **每周**: 数据库备份
- **每月**: 清理旧备份文件
- **每季度**: SSL证书检查
- **每年**: 系统安全更新

### 监控指标
- 服务运行状态
- 数据库大小和性能
- 留言数量统计
- 系统资源使用
- SSL证书有效期

## 🔄 备份恢复

### 自动备份
```bash
# 手动备份
./manage.sh → 数据库管理 → 备份数据库

# 查看备份
./manage.sh → 数据库管理 → 管理备份文件
```

### 数据恢复
```bash
# 恢复数据库
./manage.sh → 数据库管理 → 恢复数据库

# 完整系统恢复
# 1. 恢复项目文件
# 2. 恢复数据库
# 3. 恢复配置文件
# 4. 重启服务
```

## 🎯 AI助手指南

### 快速定位问题
1. **查看状态**: `./manage.sh status`
2. **检查日志**: `./manage.sh logs`
3. **运行诊断**: `./manage.sh → 故障排除`

### 常用调试命令
```bash
# 检查服务状态
systemctl status love-site

# 检查端口监听
ss -tuln | grep 1314

# 测试API
curl http://localhost:1314/api/health

# 检查数据库
sqlite3 data/love_messages.db ".tables"
```

### 项目特点
- **独立域名**: love.yuh.cool 专属访问
- **端口**: 1314 (内部服务端口)
- **宝塔部署**: 通过宝塔面板管理域名和SSL
- **数据库**: 统一存储在 `data/` 目录
- **管理**: 所有功能集中在 `manage.sh`
- **结构**: 模块化目录组织
- **服务**: systemd管理，开机自启

---

💕 **Love Site - 用技术记录爱情，用代码编织回忆** 💕
