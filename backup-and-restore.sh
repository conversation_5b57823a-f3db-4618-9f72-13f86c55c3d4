#!/bin/bash

# 备份和恢复脚本 - 保留关键功能文件
# 作者：AI Assistant
# 日期：$(date)

set -e

BACKUP_DIR="/tmp/love-backup"
LOVE_DIR="/root/workspace/love"
REPO_URL="https://github.com/CoolKingW/workspace.git"
TEMP_REPO="/tmp/workspace-repo"

echo "🔄 开始备份和恢复流程..."

# 第一步：备份关键文件
echo "📦 第一步：备份关键文件..."

# 创建备份目录结构
mkdir -p "$BACKUP_DIR"/{config,scripts,data,cloudinary,tools,docs}

# 备份配置文件
echo "  - 备份配置文件..."
cp "$LOVE_DIR/config.js" "$BACKUP_DIR/"
cp -r "$LOVE_DIR/config/" "$BACKUP_DIR/config/" 2>/dev/null || true

# 备份Cloudinary相关文件
echo "  - 备份Cloudinary相关文件..."
cp "$LOVE_DIR/cloudinary-load-balancer.js" "$BACKUP_DIR/cloudinary/" 2>/dev/null || true
cp "$LOVE_DIR/cloudinary-setup.js" "$BACKUP_DIR/cloudinary/" 2>/dev/null || true
cp "$LOVE_DIR/hybrid-cdn-manager.js" "$BACKUP_DIR/cloudinary/" 2>/dev/null || true

# 备份脚本目录
echo "  - 备份脚本目录..."
cp -r "$LOVE_DIR/scripts/" "$BACKUP_DIR/scripts/" 2>/dev/null || true

# 备份数据文件
echo "  - 备份数据文件..."
cp -r "$LOVE_DIR/data/" "$BACKUP_DIR/data/" 2>/dev/null || true
cp "$LOVE_DIR/modern-quotes-data.js" "$BACKUP_DIR/" 2>/dev/null || true

# 备份其他重要功能文件
echo "  - 备份其他重要功能文件..."
cp "$LOVE_DIR/preload-manager.js" "$BACKUP_DIR/" 2>/dev/null || true
cp "$LOVE_DIR/performance-monitor.js" "$BACKUP_DIR/" 2>/dev/null || true
cp "$LOVE_DIR/device-detection-utils.js" "$BACKUP_DIR/" 2>/dev/null || true
cp "$LOVE_DIR/dynamic-styles.js" "$BACKUP_DIR/" 2>/dev/null || true
cp "$LOVE_DIR/simple-video-manager.js" "$BACKUP_DIR/" 2>/dev/null || true
cp "$LOVE_DIR/performance-report.js" "$BACKUP_DIR/" 2>/dev/null || true
cp "$LOVE_DIR/page-navigation.js" "$BACKUP_DIR/" 2>/dev/null || true

# 备份工具目录
echo "  - 备份工具目录..."
cp -r "$LOVE_DIR/tools/" "$BACKUP_DIR/tools/" 2>/dev/null || true

# 备份文档
echo "  - 备份重要文档..."
cp -r "$LOVE_DIR/docs/" "$BACKUP_DIR/docs/" 2>/dev/null || true

# 备份package.json和yarn.lock
echo "  - 备份依赖配置..."
cp "$LOVE_DIR/package.json" "$BACKUP_DIR/" 2>/dev/null || true
cp "$LOVE_DIR/package-lock.json" "$BACKUP_DIR/" 2>/dev/null || true
cp "$LOVE_DIR/yarn.lock" "$BACKUP_DIR/" 2>/dev/null || true

# 备份背景视频目录（如果存在压缩视频）
echo "  - 备份背景视频..."
cp -r "$LOVE_DIR/background/" "$BACKUP_DIR/" 2>/dev/null || true

echo "✅ 备份完成！"

# 第二步：克隆GitHub仓库
echo "🌐 第二步：克隆GitHub仓库..."
rm -rf "$TEMP_REPO"
git clone "$REPO_URL" "$TEMP_REPO"

if [ ! -d "$TEMP_REPO/love" ]; then
    echo "❌ 错误：仓库中没有找到love目录"
    exit 1
fi

echo "✅ 仓库克隆完成！"

# 第三步：备份当前love目录
echo "📁 第三步：备份当前love目录..."
mv "$LOVE_DIR" "${LOVE_DIR}-old-$(date +%Y%m%d-%H%M%S)"

# 第四步：复制新的love目录
echo "📋 第四步：复制新的love目录..."
cp -r "$TEMP_REPO/love" "$LOVE_DIR"

echo "✅ 新版本复制完成！"

# 第五步：恢复关键文件
echo "🔄 第五步：恢复关键文件..."

# 恢复配置文件
echo "  - 恢复配置文件..."
cp "$BACKUP_DIR/config.js" "$LOVE_DIR/"
cp -r "$BACKUP_DIR/config/" "$LOVE_DIR/" 2>/dev/null || true

# 恢复Cloudinary相关文件
echo "  - 恢复Cloudinary相关文件..."
cp "$BACKUP_DIR/cloudinary/"* "$LOVE_DIR/" 2>/dev/null || true

# 恢复脚本目录
echo "  - 恢复脚本目录..."
cp -r "$BACKUP_DIR/scripts/" "$LOVE_DIR/" 2>/dev/null || true

# 恢复数据文件
echo "  - 恢复数据文件..."
cp -r "$BACKUP_DIR/data/" "$LOVE_DIR/" 2>/dev/null || true
cp "$BACKUP_DIR/modern-quotes-data.js" "$LOVE_DIR/" 2>/dev/null || true

# 恢复其他重要功能文件
echo "  - 恢复其他重要功能文件..."
cp "$BACKUP_DIR/preload-manager.js" "$LOVE_DIR/" 2>/dev/null || true
cp "$BACKUP_DIR/performance-monitor.js" "$LOVE_DIR/" 2>/dev/null || true
cp "$BACKUP_DIR/device-detection-utils.js" "$LOVE_DIR/" 2>/dev/null || true
cp "$BACKUP_DIR/dynamic-styles.js" "$LOVE_DIR/" 2>/dev/null || true
cp "$BACKUP_DIR/simple-video-manager.js" "$LOVE_DIR/" 2>/dev/null || true
cp "$BACKUP_DIR/performance-report.js" "$LOVE_DIR/" 2>/dev/null || true
cp "$BACKUP_DIR/page-navigation.js" "$LOVE_DIR/" 2>/dev/null || true

# 恢复工具目录
echo "  - 恢复工具目录..."
cp -r "$BACKUP_DIR/tools/" "$LOVE_DIR/" 2>/dev/null || true

# 恢复文档
echo "  - 恢复重要文档..."
cp -r "$BACKUP_DIR/docs/" "$LOVE_DIR/" 2>/dev/null || true

# 恢复依赖配置
echo "  - 恢复依赖配置..."
cp "$BACKUP_DIR/package.json" "$LOVE_DIR/" 2>/dev/null || true
cp "$BACKUP_DIR/package-lock.json" "$LOVE_DIR/" 2>/dev/null || true
cp "$BACKUP_DIR/yarn.lock" "$LOVE_DIR/" 2>/dev/null || true

# 恢复背景视频
echo "  - 恢复背景视频..."
cp -r "$BACKUP_DIR/background/" "$LOVE_DIR/" 2>/dev/null || true

echo "✅ 关键文件恢复完成！"

# 第六步：清理临时文件
echo "🧹 第六步：清理临时文件..."
rm -rf "$TEMP_REPO"

echo "✅ 清理完成！"

# 第七步：验证恢复结果
echo "🔍 第七步：验证恢复结果..."

echo "检查关键文件是否存在："
echo "  - config.js: $([ -f "$LOVE_DIR/config.js" ] && echo "✅" || echo "❌")"
echo "  - scripts目录: $([ -d "$LOVE_DIR/scripts" ] && echo "✅" || echo "❌")"
echo "  - cloudinary文件: $([ -f "$LOVE_DIR/cloudinary-load-balancer.js" ] && echo "✅" || echo "❌")"
echo "  - 数据目录: $([ -d "$LOVE_DIR/data" ] && echo "✅" || echo "❌")"
echo "  - 背景视频: $([ -d "$LOVE_DIR/background" ] && echo "✅" || echo "❌")"

echo ""
echo "🎉 备份和恢复流程完成！"
echo ""
echo "📋 总结："
echo "  - 旧版本已备份到: ${LOVE_DIR}-old-$(date +%Y%m%d-%H%M%S)"
echo "  - 新版本已部署到: $LOVE_DIR"
echo "  - 关键功能文件已保留"
echo "  - 备份文件保存在: $BACKUP_DIR"
echo ""
echo "⚠️  请检查以下内容："
echo "  1. 验证config.js中的Cloudinary配置是否正确"
echo "  2. 检查视频文件是否完整"
echo "  3. 测试网站功能是否正常"
echo "  4. 如有问题，可从备份目录恢复文件"
