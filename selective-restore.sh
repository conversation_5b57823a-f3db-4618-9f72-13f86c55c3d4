#!/bin/bash

# 选择性恢复关键文件
set -e

BACKUP_DIR="/root/workspace/love-old-20250730-225611"
LOVE_DIR="/root/workspace/love"

echo "🔄 开始选择性恢复关键文件..."

# 复制关键文件（不包括大的视频文件）
echo "📋 复制关键配置和脚本文件..."

# 基础文件
cp "$BACKUP_DIR/config.js" "$LOVE_DIR/"
cp "$BACKUP_DIR/server.js" "$LOVE_DIR/"
cp "$BACKUP_DIR/script.js" "$LOVE_DIR/"
cp "$BACKUP_DIR/style.css" "$LOVE_DIR/"
cp "$BACKUP_DIR/pages.css" "$LOVE_DIR/"
cp "$BACKUP_DIR/package.json" "$LOVE_DIR/"
cp "$BACKUP_DIR/package-lock.json" "$LOVE_DIR/"
cp "$BACKUP_DIR/.env" "$LOVE_DIR/" 2>/dev/null || true
cp "$BACKUP_DIR/.htaccess" "$LOVE_DIR/" 2>/dev/null || true

# Cloudinary相关文件
cp "$BACKUP_DIR/cloudinary-load-balancer.js" "$LOVE_DIR/"
cp "$BACKUP_DIR/cloudinary-setup.js" "$LOVE_DIR/"
cp "$BACKUP_DIR/hybrid-cdn-manager.js" "$LOVE_DIR/"

# 功能文件
cp "$BACKUP_DIR/device-detection-utils.js" "$LOVE_DIR/"
cp "$BACKUP_DIR/dynamic-styles.js" "$LOVE_DIR/"
cp "$BACKUP_DIR/preload-manager.js" "$LOVE_DIR/"
cp "$BACKUP_DIR/performance-monitor.js" "$LOVE_DIR/"
cp "$BACKUP_DIR/page-navigation.js" "$LOVE_DIR/"
cp "$BACKUP_DIR/simple-video-manager.js" "$LOVE_DIR/"
cp "$BACKUP_DIR/performance-report.js" "$LOVE_DIR/"
cp "$BACKUP_DIR/service-worker.js" "$LOVE_DIR/"
cp "$BACKUP_DIR/modern-quotes-data.js" "$LOVE_DIR/"
cp "$BACKUP_DIR/quick-test-commands.js" "$LOVE_DIR/"

# 复制目录
echo "📁 复制目录..."
cp -r "$BACKUP_DIR/config/" "$LOVE_DIR/" 2>/dev/null || true
cp -r "$BACKUP_DIR/data/" "$LOVE_DIR/" 2>/dev/null || true
cp -r "$BACKUP_DIR/fonts/" "$LOVE_DIR/" 2>/dev/null || true
cp -r "$BACKUP_DIR/html/" "$LOVE_DIR/" 2>/dev/null || true
cp -r "$BACKUP_DIR/scripts/" "$LOVE_DIR/" 2>/dev/null || true
cp -r "$BACKUP_DIR/docs/" "$LOVE_DIR/" 2>/dev/null || true
cp -r "$BACKUP_DIR/tools/" "$LOVE_DIR/" 2>/dev/null || true
cp -r "$BACKUP_DIR/test/" "$LOVE_DIR/" 2>/dev/null || true

# 只复制home目录的背景视频（压缩版）
echo "🎬 复制关键背景视频..."
mkdir -p "$LOVE_DIR/background/home"
cp "$BACKUP_DIR/background/home/<USER>" "$LOVE_DIR/background/home/" 2>/dev/null || echo "警告：压缩视频未找到"

echo "✅ 选择性恢复完成！"

# 验证
echo "🔍 验证关键文件..."
echo "  - config.js: $([ -f "$LOVE_DIR/config.js" ] && echo "✅" || echo "❌")"
echo "  - server.js: $([ -f "$LOVE_DIR/server.js" ] && echo "✅" || echo "❌")"
echo "  - cloudinary文件: $([ -f "$LOVE_DIR/cloudinary-load-balancer.js" ] && echo "✅" || echo "❌")"
echo "  - scripts目录: $([ -d "$LOVE_DIR/scripts" ] && echo "✅" || echo "❌")"
echo "  - 压缩视频: $([ -f "$LOVE_DIR/background/home/<USER>" ] && echo "✅" || echo "❌")"

echo ""
echo "🎉 恢复完成！现在可以测试网站功能。"
